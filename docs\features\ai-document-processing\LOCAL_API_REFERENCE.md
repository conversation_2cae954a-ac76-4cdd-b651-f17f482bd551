# 📡 مرجع API النظام المحلي

## 📋 نظرة عامة

يوفر النظام المحلي مجموعة شاملة من APIs للتفاعل مع نماذج الذكاء الاصطناعي المحلية. جميع العمليات تتم محلياً في المتصفح بدون طلبات خارجية.

## 🔗 نقاط النهاية الرئيسية

### 📤 رفع ومعالجة المستندات

#### `POST /api/ai-document-processing/upload`

**الوصف:** رفع مستند وبدء المعالجة المحلية

**المعاملات:**
```typescript
interface UploadRequest {
  file: File;                    // الملف المرفوع
  userType: 'merchant' | 'representative';
  processingOptions?: {
    priority?: number;           // أولوية المعالجة (1-5)
    enableOptimization?: boolean; // تفعيل التحسين
    modelPreferences?: string[]; // تفضيلات النماذج
  };
}
```

**الاستجابة:**
```typescript
interface UploadResponse {
  success: boolean;
  message: string;
  data: {
    processingId: string;
    status: 'processing';
    currentStage: 'ocr';
    estimatedTime: string;
    processingType: 'local';
    benefits: string[];
  };
}
```

**مثال:**
```typescript
const formData = new FormData();
formData.append('file', file);
formData.append('userType', 'merchant');

const response = await fetch('/api/ai-document-processing/upload', {
  method: 'POST',
  body: formData
});

const result = await response.json();
console.log('معرف المعالجة:', result.data.processingId);
```

---

### 📊 حالة المعالجة

#### `GET /api/ai-document-processing/status/{processingId}`

**الوصف:** الحصول على حالة المعالجة الحالية

**الاستجابة:**
```typescript
interface ProcessingStatus {
  success: boolean;
  data: {
    id: string;
    status: 'processing' | 'completed' | 'failed';
    currentStage: 'ocr' | 'ner' | 'classification' | 'completed';
    progress: number;            // 0-100
    ocrResult?: OCRResult;
    nerResult?: NERResult;
    classificationResult?: ClassificationResult;
    overallConfidence?: number;
    processingTime?: number;
    errors?: string[];
    warnings?: string[];
  };
}
```

**مثال:**
```typescript
const response = await fetch(`/api/ai-document-processing/status/${processingId}`);
const status = await response.json();

if (status.data.status === 'completed') {
  console.log('المعالجة مكتملة:', status.data);
}
```

---

### 🧠 إدارة النماذج المحلية

#### `GET /api/ai-document-processing/models-status`

**الوصف:** الحصول على حالة النماذج المحلية

**الاستجابة:**
```typescript
interface ModelsStatusResponse {
  success: boolean;
  data: {
    models: ModelInfo[];
    systemStats: {
      totalMemoryUsage: number;
      maxMemoryLimit: number;
      availableMemory: number;
      loadedModelsCount: number;
      systemReady: boolean;
      processingCapability: 'high' | 'medium' | 'low';
    };
    capabilities: {
      ocr: boolean;
      ner: boolean;
      classification: boolean;
    };
    recommendations: string[];
  };
}

interface ModelInfo {
  id: string;
  name: string;
  task: 'ocr' | 'ner' | 'classification';
  status: 'not_loaded' | 'downloading' | 'loading' | 'ready' | 'error';
  progress: number;
  size: string;
  memoryUsage: number;
  lastUsed?: number;
  error?: string;
}
```

**مثال:**
```typescript
const response = await fetch('/api/ai-document-processing/models-status');
const modelsStatus = await response.json();

console.log('النماذج المحملة:', modelsStatus.data.systemStats.loadedModelsCount);
console.log('الذاكرة المستخدمة:', modelsStatus.data.systemStats.totalMemoryUsage);
```

#### `POST /api/ai-document-processing/models-status`

**الوصف:** إدارة النماذج المحلية

**المعاملات:**
```typescript
interface ModelManagementRequest {
  action: 'preload' | 'unload' | 'cleanup';
  modelId?: string;             // مطلوب للإجراءات المحددة
  options?: {
    force?: boolean;            // إجبار العملية
    priority?: number;          // أولوية العملية
  };
}
```

**الاستجابة:**
```typescript
interface ModelManagementResponse {
  success: boolean;
  message: string;
  data: {
    action: string;
    modelsAffected?: string[];
    memoryFreed?: number;
    estimatedTime?: string;
    benefits?: string[];
  };
}
```

**أمثلة:**
```typescript
// تحميل النماذج الأساسية مسبقاً
const preloadResponse = await fetch('/api/ai-document-processing/models-status', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ action: 'preload' })
});

// تنظيف الذاكرة
const cleanupResponse = await fetch('/api/ai-document-processing/models-status', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ action: 'cleanup' })
});

// إلغاء تحميل نموذج محدد
const unloadResponse = await fetch('/api/ai-document-processing/models-status', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ 
    action: 'unload', 
    modelId: 'specific-model-id' 
  })
});
```

---

### ⚡ التحسين والأداء

#### `GET /api/ai-document-processing/optimization`

**الوصف:** الحصول على إحصائيات التحسين

**الاستجابة:**
```typescript
interface OptimizationStats {
  success: boolean;
  data: {
    totalOptimizations: number;
    successRate: number;
    totalMemoryFreed: number;
    averagePerformanceGain: number;
    recentOptimizations: OptimizationRecord[];
    recommendations: string[];
    systemHealth: {
      overall: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
      score: number;
      components: HealthComponents;
    };
  };
}
```

#### `POST /api/ai-document-processing/optimization`

**الوصف:** تشغيل تحسين يدوي

**المعاملات:**
```typescript
interface OptimizationRequest {
  strategies?: string[];        // استراتيجيات محددة
  aggressiveMode?: boolean;     // الوضع العدواني
  targetMemoryUsage?: number;   // الهدف للذاكرة (MB)
}
```

**مثال:**
```typescript
const optimizationResponse = await fetch('/api/ai-document-processing/optimization', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    strategies: ['memory_cleanup', 'cache_optimization'],
    aggressiveMode: true
  })
});

const result = await optimizationResponse.json();
console.log('نتائج التحسين:', result.data);
```

---

## 🔧 APIs الخدمات المحلية

### 🖼️ خدمة OCR المحلية

```typescript
import { LocalOCRService } from '@/services/localOCRService';

const ocrService = LocalOCRService.getInstance();

// استخراج النص
const result = await ocrService.extractText(
  imageUrl, 
  mimeType,
  (progress) => console.log(`OCR: ${progress.percentage}%`)
);

console.log('النص المستخرج:', result.extractedText);
console.log('الثقة:', result.confidence);
console.log('اللغة المكتشفة:', result.language);
```

### 🔍 خدمة NER المحلية

```typescript
import { LocalNERService } from '@/services/localNERService';

const nerService = LocalNERService.getInstance();

// استخلاص الكيانات
const result = await nerService.extractEntitiesAndData(
  text,
  language,
  documentType,
  (progress) => console.log(`NER: ${progress.percentage}%`)
);

console.log('الكيانات:', result.entities);
console.log('البيانات المهيكلة:', result.extractedData);
```

### 📊 خدمة التصنيف المحلية

```typescript
import { LocalClassificationService } from '@/services/localClassificationService';

const classificationService = LocalClassificationService.getInstance();

// تصنيف واتخاذ قرار
const result = await classificationService.classifyAndDecide(
  extractedData,
  originalText,
  userType,
  documentType,
  (progress) => console.log(`Classification: ${progress.percentage}%`)
);

console.log('نوع المستند:', result.documentType);
console.log('القرار:', result.decision);
console.log('التبرير:', result.reasoning);
```

---

## 🎛️ إدارة النماذج المتقدمة

### 📦 مدير النماذج المتقدم

```typescript
import { advancedModelManager } from '@/services/advancedModelManager';

// تحميل نموذج مع خيارات متقدمة
await advancedModelManager.loadModel('Xenova/trocr-base-printed', 'ocr', {
  priority: 1,
  preload: true,
  onProgress: (progress) => {
    console.log(`تحميل ${progress.modelId}: ${progress.progress}%`);
    console.log(`المرحلة: ${progress.stage}`);
  },
  timeout: 60000
});

// الحصول على إحصائيات مفصلة
const detailedStats = advancedModelManager.getDetailedMemoryStats();
console.log('تفصيل النماذج:', detailedStats.modelBreakdown);
console.log('التوصيات:', detailedStats.recommendations);

// تحميل مسبق للنماذج الأساسية
await advancedModelManager.preloadEssentialModels((progress, current) => {
  console.log(`التقدم الإجمالي: ${progress}%`);
  console.log(`النموذج الحالي: ${current}`);
});
```

### ⚡ محسن الأداء

```typescript
import { modelOptimizer } from '@/services/modelOptimizer';

// تشغيل تحسين شامل
const results = await modelOptimizer.runManualOptimization();
console.log('نتائج التحسين:', results);

// الحصول على تقرير مفصل
const report = modelOptimizer.getOptimizationReport();
console.log('إجمالي التحسينات:', report.totalOptimizations);
console.log('معدل النجاح:', report.successRate);
console.log('الذاكرة المحررة:', report.totalMemoryFreed);

// تحديث الإعدادات
modelOptimizer.updateConfig({
  enableAutoOptimization: true,
  optimizationInterval: 60000,  // دقيقة واحدة
  memoryThreshold: 75,          // 75%
  aggressiveMode: false
});
```

### 🏥 مراقب الصحة

```typescript
import { systemHealthMonitor } from '@/services/systemHealthMonitor';

// فحص صحة شامل
const health = await systemHealthMonitor.performHealthCheck();
console.log('الحالة العامة:', health.overall);
console.log('النقاط:', health.score);
console.log('التوصيات:', health.recommendations);

// الحصول على التنبيهات النشطة
const currentHealth = systemHealthMonitor.getCurrentHealth();
const activeAlerts = currentHealth?.alerts.filter(alert => !alert.acknowledged);
console.log('التنبيهات النشطة:', activeAlerts);

// الاعتراف بتنبيه
systemHealthMonitor.acknowledgeAlert('alert-id');
```

---

## 🔄 أحداث النظام

### 📡 الاستماع للأحداث

```typescript
// أحداث تحميل النماذج
window.addEventListener('model-load-start', (event) => {
  console.log('بدء تحميل النموذج:', event.detail.modelId);
});

window.addEventListener('model-load-complete', (event) => {
  console.log('اكتمل تحميل النموذج:', event.detail.modelId);
  console.log('وقت التحميل:', event.detail.loadTime);
});

// أحداث المعالجة
window.addEventListener('processing-stage-change', (event) => {
  console.log('تغيير المرحلة:', event.detail.stage);
  console.log('التقدم:', event.detail.progress);
});

window.addEventListener('processing-complete', (event) => {
  console.log('اكتملت المعالجة:', event.detail.result);
});

// أحداث التحسين
window.addEventListener('optimization-complete', (event) => {
  console.log('اكتمل التحسين:', event.detail.results);
});

// أحداث الصحة
window.addEventListener('health-alert', (event) => {
  console.log('تنبيه صحة النظام:', event.detail.alert);
});
```

---

## 🛡️ معالجة الأخطاء

### ❌ أنواع الأخطاء الشائعة

```typescript
try {
  await advancedModelManager.loadModel(modelId, task);
} catch (error) {
  if (error.message.includes('الذاكرة المتاحة غير كافية')) {
    // تحرير الذاكرة وإعادة المحاولة
    await modelOptimizer.runManualOptimization(['memory_cleanup']);
    await advancedModelManager.loadModel(modelId, task);
  } else if (error.message.includes('انتهت مهلة تحميل النموذج')) {
    // زيادة المهلة الزمنية
    await advancedModelManager.loadModel(modelId, task, { timeout: 180000 });
  } else {
    console.error('خطأ غير متوقع:', error);
  }
}
```

### 🔧 استراتيجيات الاستعادة

```typescript
// استراتيجية الاستعادة التلقائية
const processWithFallback = async (documentUrl: string) => {
  try {
    // محاولة المعالجة العادية
    return await aiService.processDocument(documentUrl, ...args);
  } catch (error) {
    console.warn('فشلت المعالجة الأساسية، جاري المحاولة مع نماذج احتياطية...');
    
    // تحرير الذاكرة
    await modelOptimizer.runManualOptimization(['memory_cleanup']);
    
    // إعادة المحاولة مع نماذج أصغر
    return await aiService.processDocumentWithFallback(documentUrl, ...args);
  }
};
```

---

## 📊 مراقبة الأداء

### 📈 جمع المقاييس

```typescript
// مراقبة الأداء المستمرة
const performanceMonitor = {
  startTime: Date.now(),
  
  measureOperation: async (operation: () => Promise<any>, name: string) => {
    const start = Date.now();
    const memoryBefore = (performance as any).memory?.usedJSHeapSize || 0;
    
    try {
      const result = await operation();
      const duration = Date.now() - start;
      const memoryAfter = (performance as any).memory?.usedJSHeapSize || 0;
      
      console.log(`📊 ${name}:`, {
        duration: `${duration}ms`,
        memoryIncrease: `${((memoryAfter - memoryBefore) / 1024 / 1024).toFixed(2)}MB`,
        success: true
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      console.error(`❌ ${name} فشل في ${duration}ms:`, error);
      throw error;
    }
  }
};

// استخدام مراقب الأداء
const result = await performanceMonitor.measureOperation(
  () => advancedModelManager.loadModel('model-id', 'ocr'),
  'تحميل نموذج OCR'
);
```

---

## 🎯 أفضل الممارسات

### ✅ التوصيات

1. **تحميل النماذج مسبقاً** في بداية التطبيق
2. **مراقبة استخدام الذاكرة** بانتظام
3. **تفعيل التحسين التلقائي** للأداء الأمثل
4. **معالجة الأخطاء** بشكل شامل
5. **استخدام أحداث النظام** للتحديثات المباشرة

### ⚠️ تجنب

1. **تحميل نماذج متعددة** بدون ضرورة
2. **تجاهل تنبيهات الذاكرة**
3. **عدم تنظيف الموارد** بعد الاستخدام
4. **تشغيل عمليات متوازية** مكثفة

---

هذا المرجع يوفر دليلاً شاملاً لاستخدام جميع APIs النظام المحلي بكفاءة وأمان! 🚀
