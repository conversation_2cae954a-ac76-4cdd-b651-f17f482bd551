// src/services/localClassificationService.ts - خدمة التصنيف واتخاذ القرار المحلية

import { modelManager } from './localModelManager';
import { ClassificationResult } from './huggingFaceAIService';
import { 
  TRANSFORMERS_CONFIG, 
  SupportedLanguage,
  CLASSIFICATION_MODELS 
} from '../config/transformersConfig';

// قواعد اتخاذ القرار المحسّنة
const DECISION_RULES = {
  // قواعد الموافقة للتجار
  merchant: {
    identity: {
      required_fields: ['nationalId', 'name', 'birthDate'],
      min_confidence: 0.8,
      auto_approve_threshold: 0.9
    },
    business: {
      required_fields: ['registrationNumber', 'businessName', 'ownerName'],
      min_confidence: 0.75,
      auto_approve_threshold: 0.85
    }
  },
  
  // قواعد الموافقة للمندوبين
  representative: {
    identity: {
      required_fields: ['nationalId', 'name'],
      min_confidence: 0.7,
      auto_approve_threshold: 0.8
    },
    business: {
      required_fields: ['registrationNumber', 'businessName'],
      min_confidence: 0.7,
      auto_approve_threshold: 0.8
    }
  }
};

// أوزان عوامل القرار
const DECISION_WEIGHTS = {
  data_completeness: 0.4,    // اكتمال البيانات
  confidence_score: 0.3,     // مستوى الثقة
  document_quality: 0.2,     // جودة المستند
  consistency_check: 0.1     // التحقق من التناسق
};

// تصنيفات المستندات
const DOCUMENT_CLASSIFICATIONS = {
  identity: {
    ar: ['هوية وطنية', 'بطاقة شخصية', 'جواز سفر', 'رخصة قيادة'],
    en: ['national id', 'identity card', 'passport', 'driver license']
  },
  business: {
    ar: ['سجل تجاري', 'رخصة تجارية', 'شهادة تأسيس', 'ترخيص مهني'],
    en: ['commercial registration', 'business license', 'trade license', 'professional license']
  },
  financial: {
    ar: ['كشف حساب', 'فاتورة', 'إيصال', 'شهادة راتب'],
    en: ['bank statement', 'invoice', 'receipt', 'salary certificate']
  }
};

/**
 * خدمة التصنيف واتخاذ القرار المحلية
 */
export class LocalClassificationService {
  private static instance: LocalClassificationService;

  private constructor() {}

  static getInstance(): LocalClassificationService {
    if (!LocalClassificationService.instance) {
      LocalClassificationService.instance = new LocalClassificationService();
    }
    return LocalClassificationService.instance;
  }

  /**
   * تصنيف المستند واتخاذ قرار الموافقة
   */
  async classifyAndDecide(
    extractedData: Record<string, any>,
    originalText: string,
    userType: 'merchant' | 'representative',
    documentType?: string,
    onProgress?: (progress: { stage: string; percentage: number }) => void
  ): Promise<ClassificationResult> {
    const startTime = Date.now();
    
    try {
      // المرحلة 1: تصنيف نوع المستند
      onProgress?.({ stage: 'تصنيف نوع المستند', percentage: 10 });
      const classifiedType = documentType || await this.classifyDocumentType(originalText);
      
      // المرحلة 2: تحليل جودة البيانات
      onProgress?.({ stage: 'تحليل جودة البيانات', percentage: 30 });
      const dataQuality = this.analyzeDataQuality(extractedData, classifiedType);
      
      // المرحلة 3: التحقق من اكتمال البيانات
      onProgress?.({ stage: 'التحقق من اكتمال البيانات', percentage: 50 });
      const completeness = this.checkDataCompleteness(extractedData, classifiedType, userType);
      
      // المرحلة 4: تحليل التناسق
      onProgress?.({ stage: 'تحليل تناسق البيانات', percentage: 70 });
      const consistency = this.checkDataConsistency(extractedData, originalText);
      
      // المرحلة 5: حساب النقاط الإجمالية
      onProgress?.({ stage: 'حساب النقاط الإجمالية', percentage: 85 });
      const overallScore = this.calculateOverallScore({
        dataQuality,
        completeness,
        consistency
      });
      
      // المرحلة 6: اتخاذ القرار النهائي
      onProgress?.({ stage: 'اتخاذ القرار النهائي', percentage: 95 });
      const decision = this.makeDecision(overallScore, classifiedType, userType, extractedData);
      
      onProgress?.({ stage: 'اكتمل', percentage: 100 });
      
      const result: ClassificationResult = {
        documentType: classifiedType,
        confidence: overallScore.confidence,
        decision: decision.status,
        reasoning: decision.reasoning,
        requiredActions: decision.requiredActions,
        score: overallScore.total,
        breakdown: {
          dataCompleteness: completeness.score,
          confidenceScore: overallScore.confidence,
          documentQuality: dataQuality.score,
          consistencyCheck: consistency.score
        },
        processingTime: Date.now() - startTime,
        metadata: {
          userType,
          extractedFieldsCount: Object.keys(extractedData).length,
          missingFields: completeness.missingFields,
          qualityIssues: dataQuality.issues,
          consistencyIssues: consistency.issues
        }
      };

      console.log(`✅ التصنيف مكتمل: ${classifiedType}, القرار: ${decision.status}, النقاط: ${overallScore.total}`);
      return result;

    } catch (error) {
      console.error('❌ خطأ في التصنيف المحلي:', error);
      
      // قرار احتياطي بناءً على القواعد البسيطة
      return await this.fallbackDecision(extractedData, originalText, userType);
    }
  }

  /**
   * تصنيف نوع المستند
   */
  private async classifyDocumentType(text: string): Promise<string> {
    try {
      // محاولة استخدام النموذج المحلي
      const modelId = CLASSIFICATION_MODELS.document_classifier.id;
      const pipeline = await modelManager.loadModel(modelId, 'classification');
      
      const candidates = ['identity', 'business', 'financial', 'other'];
      const result = await pipeline(text, { candidate_labels: candidates });
      
      if (result && result.labels && result.labels.length > 0) {
        return result.labels[0];
      }
    } catch (error) {
      console.warn('⚠️ فشل في تصنيف المستند بالنموذج، استخدام القواعد:', error);
    }
    
    // استخدام القواعد البسيطة كبديل
    return this.classifyWithRules(text);
  }

  /**
   * تصنيف المستند باستخدام القواعد
   */
  private classifyWithRules(text: string): string {
    const lowerText = text.toLowerCase();
    
    // البحث عن كلمات مفتاحية
    const identityKeywords = ['هوية', 'بطاقة', 'جواز', 'id', 'identity', 'passport'];
    const businessKeywords = ['سجل', 'تجاري', 'رخصة', 'business', 'license', 'registration'];
    const financialKeywords = ['حساب', 'فاتورة', 'إيصال', 'bank', 'invoice', 'receipt'];
    
    if (identityKeywords.some(keyword => lowerText.includes(keyword))) {
      return 'identity';
    } else if (businessKeywords.some(keyword => lowerText.includes(keyword))) {
      return 'business';
    } else if (financialKeywords.some(keyword => lowerText.includes(keyword))) {
      return 'financial';
    }
    
    return 'other';
  }

  /**
   * تحليل جودة البيانات
   */
  private analyzeDataQuality(data: Record<string, any>, documentType: string): {
    score: number;
    issues: string[];
  } {
    const issues: string[] = [];
    let score = 1.0;
    
    // التحقق من وجود بيانات
    if (Object.keys(data).length === 0) {
      issues.push('لا توجد بيانات مستخرجة');
      score = 0;
      return { score, issues };
    }
    
    // التحقق من جودة كل حقل
    Object.entries(data).forEach(([field, value]) => {
      if (!value || (typeof value === 'string' && value.trim().length < 2)) {
        issues.push(`حقل ${field} فارغ أو قصير جداً`);
        score -= 0.1;
      }
      
      // التحقق من صحة التواريخ
      if (field.includes('date') || field.includes('تاريخ')) {
        if (!this.isValidDate(value)) {
          issues.push(`تاريخ غير صحيح في حقل ${field}`);
          score -= 0.15;
        }
      }
      
      // التحقق من صحة الأرقام
      if (field.includes('number') || field.includes('رقم')) {
        if (!this.isValidNumber(value)) {
          issues.push(`رقم غير صحيح في حقل ${field}`);
          score -= 0.15;
        }
      }
    });
    
    return { score: Math.max(score, 0), issues };
  }

  /**
   * التحقق من اكتمال البيانات
   */
  private checkDataCompleteness(
    data: Record<string, any>,
    documentType: string,
    userType: 'merchant' | 'representative'
  ): { score: number; missingFields: string[] } {
    const rules = DECISION_RULES[userType][documentType as keyof typeof DECISION_RULES[typeof userType]];
    if (!rules) {
      return { score: 0.5, missingFields: ['نوع مستند غير مدعوم'] };
    }
    
    const requiredFields = rules.required_fields;
    const missingFields: string[] = [];
    
    requiredFields.forEach(field => {
      if (!data[field] || (typeof data[field] === 'string' && data[field].trim().length === 0)) {
        missingFields.push(field);
      }
    });
    
    const completenessRatio = (requiredFields.length - missingFields.length) / requiredFields.length;
    return { score: completenessRatio, missingFields };
  }

  /**
   * التحقق من تناسق البيانات
   */
  private checkDataConsistency(data: Record<string, any>, originalText: string): {
    score: number;
    issues: string[];
  } {
    const issues: string[] = [];
    let score = 1.0;
    
    // التحقق من وجود البيانات في النص الأصلي
    Object.entries(data).forEach(([field, value]) => {
      if (typeof value === 'string' && value.length > 2) {
        if (!originalText.includes(value)) {
          issues.push(`البيانات في حقل ${field} غير موجودة في النص الأصلي`);
          score -= 0.2;
        }
      }
    });
    
    // التحقق من تناسق التواريخ
    const dates = Object.entries(data)
      .filter(([key]) => key.includes('date') || key.includes('تاريخ'))
      .map(([key, value]) => ({ field: key, date: new Date(value) }))
      .filter(item => !isNaN(item.date.getTime()));
    
    if (dates.length >= 2) {
      const issueDate = dates.find(d => d.field.includes('issue') || d.field.includes('إصدار'));
      const expiryDate = dates.find(d => d.field.includes('expiry') || d.field.includes('انتهاء'));
      
      if (issueDate && expiryDate && issueDate.date >= expiryDate.date) {
        issues.push('تاريخ الإصدار لا يمكن أن يكون بعد تاريخ الانتهاء');
        score -= 0.3;
      }
    }
    
    return { score: Math.max(score, 0), issues };
  }

  /**
   * حساب النقاط الإجمالية
   */
  private calculateOverallScore(factors: {
    dataQuality: { score: number };
    completeness: { score: number };
    consistency: { score: number };
  }): { total: number; confidence: number } {
    const total = 
      (factors.dataQuality.score * DECISION_WEIGHTS.data_completeness) +
      (factors.completeness.score * DECISION_WEIGHTS.confidence_score) +
      (factors.consistency.score * DECISION_WEIGHTS.consistency_check) +
      (0.8 * DECISION_WEIGHTS.document_quality); // افتراض جودة مستند جيدة
    
    const confidence = Math.min(total, 1.0);
    
    return { total, confidence };
  }

  /**
   * اتخاذ القرار النهائي
   */
  private makeDecision(
    score: { total: number; confidence: number },
    documentType: string,
    userType: 'merchant' | 'representative',
    extractedData: Record<string, any>
  ): {
    status: 'approved' | 'rejected' | 'requires_review';
    reasoning: string;
    requiredActions: string[];
  } {
    const rules = DECISION_RULES[userType][documentType as keyof typeof DECISION_RULES[typeof userType]];
    const requiredActions: string[] = [];
    
    if (!rules) {
      return {
        status: 'requires_review',
        reasoning: 'نوع المستند غير مدعوم، يتطلب مراجعة يدوية',
        requiredActions: ['مراجعة يدوية مطلوبة']
      };
    }
    
    // قرار الموافقة التلقائية
    if (score.confidence >= rules.auto_approve_threshold) {
      return {
        status: 'approved',
        reasoning: `تم قبول المستند تلقائياً - مستوى الثقة عالي (${(score.confidence * 100).toFixed(1)}%)`,
        requiredActions: []
      };
    }
    
    // قرار الرفض
    if (score.confidence < rules.min_confidence) {
      const missingFields = rules.required_fields.filter(field => !extractedData[field]);
      
      if (missingFields.length > 0) {
        requiredActions.push(`إضافة الحقول المفقودة: ${missingFields.join(', ')}`);
      }
      
      return {
        status: 'rejected',
        reasoning: `مستوى الثقة منخفض (${(score.confidence * 100).toFixed(1)}%) - بيانات غير كافية`,
        requiredActions
      };
    }
    
    // قرار المراجعة
    requiredActions.push('مراجعة البيانات المستخرجة');
    requiredActions.push('التحقق من جودة الصورة');
    
    return {
      status: 'requires_review',
      reasoning: `مستوى الثقة متوسط (${(score.confidence * 100).toFixed(1)}%) - يتطلب مراجعة`,
      requiredActions
    };
  }

  // الطرق المساعدة

  private isValidDate(dateStr: string): boolean {
    if (!dateStr || typeof dateStr !== 'string') return false;
    
    const date = new Date(dateStr);
    return !isNaN(date.getTime()) && date.getFullYear() > 1900 && date.getFullYear() < 2100;
  }

  private isValidNumber(numberStr: string): boolean {
    if (!numberStr || typeof numberStr !== 'string') return false;
    
    const cleanNumber = numberStr.replace(/[\s\-]/g, '');
    return /^\d+$/.test(cleanNumber) && cleanNumber.length >= 5;
  }

  /**
   * قرار احتياطي بناءً على القواعد البسيطة
   */
  private async fallbackDecision(
    extractedData: Record<string, any>,
    originalText: string,
    userType: 'merchant' | 'representative'
  ): Promise<ClassificationResult> {
    console.log('🔄 استخدام القرار الاحتياطي...');
    
    const documentType = this.classifyWithRules(originalText);
    const hasData = Object.keys(extractedData).length > 0;
    const confidence = hasData ? 0.6 : 0.3;
    
    return {
      documentType,
      confidence,
      decision: hasData ? 'requires_review' : 'rejected',
      reasoning: 'تم استخدام النظام الاحتياطي - مراجعة يدوية مطلوبة',
      requiredActions: ['مراجعة يدوية شاملة'],
      score: confidence,
      breakdown: {
        dataCompleteness: hasData ? 0.5 : 0,
        confidenceScore: confidence,
        documentQuality: 0.5,
        consistencyCheck: 0.5
      },
      processingTime: 0,
      metadata: {
        fallback: true,
        userType,
        extractedFieldsCount: Object.keys(extractedData).length,
        missingFields: [],
        qualityIssues: ['استخدام النظام الاحتياطي'],
        consistencyIssues: []
      }
    };
  }
}
