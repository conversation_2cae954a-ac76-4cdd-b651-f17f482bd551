# دليل إعداد وتشغيل نظام معالجة المستندات الذكي

## المتطلبات الأساسية

### 1. متطلبات النظام
- Node.js 18+ 
- Bun (بديل npm/pnpm)
- Firebase CLI
- Git

### 2. متطلبات الخدمات الخارجية
- **Hugging Face Account**: للحصول على API key
- **Firebase Project**: لقاعدة البيانات والتخزين
- **Netlify Account**: للنشر (اختياري)

## إعداد البيئة

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd Mikhla-
```

### 2. تثبيت التبعيات
```bash
bun install
```

### 3. إعداد متغيرات البيئة
إنشاء ملف `.env.local`:
```env
# Hugging Face API
HUGGING_FACE_API_KEY=your_hugging_face_api_key_here

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Firebase Admin (Server-side)
FIREBASE_ADMIN_PRIVATE_KEY=your_admin_private_key
FIREBASE_ADMIN_CLIENT_EMAIL=your_admin_client_email
FIREBASE_ADMIN_PROJECT_ID=your_project_id
```

### 4. الحصول على Hugging Face API Key
1. سجل في [Hugging Face](https://huggingface.co/)
2. اذهب إلى Settings > Access Tokens
3. أنشئ token جديد مع صلاحيات "Read"
4. انسخ الـ token وضعه في `.env.local`

### 5. إعداد Firebase
```bash
# تسجيل الدخول
firebase login

# تهيئة المشروع
firebase init

# نشر قواعد Firestore
firebase deploy --only firestore:rules

# نشر الفهارس
firebase deploy --only firestore:indexes
```

## تشغيل المشروع

### 1. التطوير المحلي
```bash
# تشغيل الخادم المحلي
bun dev

# الوصول للتطبيق
# http://localhost:3000
```

### 2. بناء الإنتاج
```bash
# بناء المشروع
bun build

# تشغيل الإنتاج محلياً
bun start
```

## اختبار النظام

### 1. اختبارات الوحدة
```bash
# تشغيل اختبارات Jest (إذا كانت متوفرة)
bun test
```

### 2. اختبارات E2E باستخدام Cypress
```bash
# تثبيت Cypress
bun add -D cypress

# فتح Cypress UI
bunx cypress open

# تشغيل الاختبارات في وضع headless
bunx cypress run

# تشغيل اختبارات محددة
bunx cypress run --spec "cypress/e2e/ai-document-processing.cy.js"
```

### 3. اختبار API Endpoints
```bash
# اختبار رفع مستند
curl -X POST http://localhost:3000/api/ai-document-processing/upload \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "document=@test-document.jpg" \
  -F "userType=merchant"

# اختبار حالة المعالجة
curl -X GET http://localhost:3000/api/ai-document-processing/status/PROCESSING_ID \
  -H "Authorization: Bearer YOUR_TOKEN"

# اختبار التاريخ
curl -X GET http://localhost:3000/api/ai-document-processing/history \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## ملفات الاختبار

### إنشاء ملفات اختبار
```bash
# إنشاء مجلد ملفات الاختبار
mkdir cypress/fixtures

# إضافة صور اختبار
# - sample-document.jpg (سجل تجاري واضح)
# - blurry-document.jpg (صورة ضبابية)
# - large-document.pdf (ملف PDF)
```

### أمثلة ملفات الاختبار
- **commercial-registration-clear.jpg**: سجل تجاري عالي الجودة
- **national-id-sample.jpg**: هوية وطنية نموذجية
- **driving-license.pdf**: رخصة قيادة PDF
- **low-quality.jpg**: صورة منخفضة الجودة للاختبار
- **unsupported.txt**: ملف غير مدعوم للاختبار

## استكشاف الأخطاء

### 1. مشاكل شائعة

#### خطأ "Hugging Face API Key not found"
```bash
# تأكد من وجود المتغير في .env.local
echo $HUGGING_FACE_API_KEY

# أعد تشغيل الخادم
bun dev
```

#### خطأ "Firebase connection failed"
```bash
# تحقق من إعدادات Firebase
firebase projects:list

# تأكد من صحة المتغيرات
cat .env.local | grep FIREBASE
```

#### خطأ "Model loading failed"
```bash
# تحقق من حالة Hugging Face API
curl -H "Authorization: Bearer YOUR_TOKEN" \
  https://api-inference.huggingface.co/models/microsoft/trocr-base-printed
```

### 2. تسجيل الأخطاء
```bash
# عرض سجلات الخادم
bun dev --verbose

# عرض سجلات Firebase
firebase functions:log

# عرض سجلات Netlify (إذا كان منشوراً)
netlify logs
```

### 3. أدوات التشخيص
```bash
# فحص حالة النماذج
curl https://api-inference.huggingface.co/status

# اختبار الاتصال بـ Firebase
firebase firestore:get users --limit 1

# فحص أحجام الملفات
du -sh .next/static/chunks/*
```

## الأداء والتحسين

### 1. مراقبة الأداء
- استخدم Chrome DevTools لمراقبة أداء الواجهة
- راقب أوقات استجابة API
- تحقق من استهلاك الذاكرة

### 2. تحسين الصور
```bash
# ضغط الصور قبل الرفع
# استخدم أدوات مثل ImageOptim أو TinyPNG
```

### 3. تحسين قاعدة البيانات
- استخدم الفهارس المناسبة
- قم بتنظيف البيانات القديمة دورياً
- راقب استهلاك Firestore

## النشر

### 1. النشر على Netlify
```bash
# ربط المشروع
netlify init

# نشر
netlify deploy --prod
```

### 2. النشر على Vercel
```bash
# تثبيت Vercel CLI
npm i -g vercel

# نشر
vercel --prod
```

### 3. متغيرات البيئة للإنتاج
تأكد من إضافة جميع متغيرات البيئة في لوحة تحكم منصة النشر:
- `HUGGING_FACE_API_KEY`
- `FIREBASE_ADMIN_PRIVATE_KEY`
- `FIREBASE_ADMIN_CLIENT_EMAIL`
- `FIREBASE_ADMIN_PROJECT_ID`

## المراقبة والصيانة

### 1. مراقبة النظام
- راقب معدل نجاح المعالجة
- تتبع أوقات الاستجابة
- راقب استهلاك API quota

### 2. النسخ الاحتياطية
```bash
# نسخ احتياطي لـ Firestore
gcloud firestore export gs://your-bucket/backup-$(date +%Y%m%d)

# نسخ احتياطي للملفات
gsutil -m cp -r gs://your-storage-bucket gs://backup-bucket
```

### 3. التحديثات
```bash
# تحديث التبعيات
bun update

# تحديث Firebase
firebase upgrade

# تحديث Cypress
bun add -D cypress@latest
```

## الدعم والمساعدة

### الموارد المفيدة
- [Hugging Face Documentation](https://huggingface.co/docs)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [Cypress Documentation](https://docs.cypress.io)

### التواصل
- GitHub Issues للمشاكل التقنية
- Discord للدعم المجتمعي
- البريد الإلكتروني للدعم المباشر

## قائمة التحقق قبل النشر

- [ ] جميع الاختبارات تمر بنجاح
- [ ] متغيرات البيئة محدثة
- [ ] قواعد Firestore منشورة
- [ ] الفهارس منشورة
- [ ] API keys صالحة
- [ ] النسخ الاحتياطية محدثة
- [ ] التوثيق محدث
- [ ] الترجمات مكتملة
