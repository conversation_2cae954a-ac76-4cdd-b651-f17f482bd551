# 💡 أمثلة عملية للنظام المحلي

## 📋 نظرة عامة

هذا الدليل يحتوي على أمثلة عملية شاملة لاستخدام النظام المحلي للذكاء الاصطناعي في سيناريوهات مختلفة.

## 🚀 البدء السريع

### 1. **إعداد أساسي للنظام**

```typescript
import { advancedModelManager } from '@/services/advancedModelManager';
import { modelOptimizer } from '@/services/modelOptimizer';
import { systemHealthMonitor } from '@/services/systemHealthMonitor';

// إعداد النظام عند بدء التطبيق
export const initializeLocalAI = async () => {
  console.log('🚀 بدء تهيئة النظام المحلي...');
  
  try {
    // 1. فحص صحة النظام
    const health = await systemHealthMonitor.performHealthCheck();
    console.log(`🏥 صحة النظام: ${health.overall} (${health.score}/100)`);
    
    // 2. تفعيل التحسين التلقائي
    modelOptimizer.updateConfig({
      enableAutoOptimization: true,
      memoryThreshold: 80,
      aggressiveMode: false
    });
    
    // 3. تحميل النماذج الأساسية مسبقاً
    await advancedModelManager.preloadEssentialModels((progress, current) => {
      console.log(`📦 تحميل النماذج: ${progress.toFixed(1)}% - ${current}`);
    });
    
    console.log('✅ تم تهيئة النظام المحلي بنجاح!');
    return true;
    
  } catch (error) {
    console.error('❌ فشل في تهيئة النظام:', error);
    return false;
  }
};
```

### 2. **معالجة مستند بسيطة**

```typescript
import { HuggingFaceAIService } from '@/services/huggingFaceAIService';

export const processSimpleDocument = async (file: File, userType: 'merchant' | 'representative') => {
  const aiService = HuggingFaceAIService.getInstance();
  
  try {
    console.log(`📄 بدء معالجة: ${file.name}`);
    
    // رفع الملف وبدء المعالجة
    const documentUrl = URL.createObjectURL(file);
    
    const result = await aiService.processDocument(
      documentUrl,
      'user-123',
      userType,
      file.name,
      file.size,
      file.type,
      (progress) => {
        console.log(`${progress.stage}: ${progress.percentage}% - ${progress.details}`);
      }
    );
    
    console.log('✅ اكتملت المعالجة:', {
      documentType: result.classificationResult.documentType,
      decision: result.classificationResult.decision,
      confidence: result.overallConfidence,
      processingTime: result.processingTime
    });
    
    return result;
    
  } catch (error) {
    console.error('❌ فشل في معالجة المستند:', error);
    throw error;
  }
};
```

## 📊 أمثلة متقدمة

### 3. **معالجة مستندات متعددة بالتوازي**

```typescript
export const processBatchDocuments = async (files: File[], userType: string) => {
  const results: any[] = [];
  const maxConcurrent = 3; // حد أقصى 3 مستندات متوازية
  
  console.log(`📚 بدء معالجة ${files.length} مستند...`);
  
  // تقسيم الملفات إلى مجموعات
  for (let i = 0; i < files.length; i += maxConcurrent) {
    const batch = files.slice(i, i + maxConcurrent);
    
    console.log(`🔄 معالجة المجموعة ${Math.floor(i / maxConcurrent) + 1}...`);
    
    // معالجة المجموعة بالتوازي
    const batchPromises = batch.map(async (file, index) => {
      try {
        const result = await processSimpleDocument(file, userType as any);
        return { success: true, file: file.name, result };
      } catch (error) {
        console.error(`❌ فشل في معالجة ${file.name}:`, error);
        return { success: false, file: file.name, error };
      }
    });
    
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
    
    // فترة راحة بين المجموعات لتجنب إرهاق النظام
    if (i + maxConcurrent < files.length) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`📊 النتائج: ${successful} نجح، ${failed} فشل`);
  return results;
};
```

### 4. **إدارة ذكية للذاكرة**

```typescript
export class SmartMemoryManager {
  private memoryThreshold = 400 * 1024 * 1024; // 400MB
  private checkInterval = 30000; // 30 ثانية
  private monitoringTimer?: NodeJS.Timeout;
  
  startMonitoring() {
    this.monitoringTimer = setInterval(() => {
      this.checkMemoryUsage();
    }, this.checkInterval);
    
    console.log('🧠 بدء مراقبة الذاكرة الذكية');
  }
  
  stopMonitoring() {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      console.log('⏹️ توقف مراقبة الذاكرة');
    }
  }
  
  private async checkMemoryUsage() {
    const stats = advancedModelManager.getDetailedMemoryStats();
    
    if (stats.totalUsage > this.memoryThreshold) {
      console.log('⚠️ تجاوز حد الذاكرة، بدء التحسين...');
      
      try {
        const results = await modelOptimizer.runManualOptimization([
          'memory_cleanup',
          'cache_optimization'
        ]);
        
        const memoryFreed = results.reduce((sum, r) => sum + (r.memoryFreed || 0), 0);
        console.log(`🧹 تم تحرير ${this.formatBytes(memoryFreed)} من الذاكرة`);
        
      } catch (error) {
        console.error('❌ فشل في تحسين الذاكرة:', error);
      }
    }
  }
  
  private formatBytes(bytes: number): string {
    return `${(bytes / 1024 / 1024).toFixed(2)}MB`;
  }
  
  async optimizeForProcessing() {
    console.log('⚡ تحسين النظام للمعالجة...');
    
    // تحميل النماذج الأساسية إذا لم تكن محملة
    const stats = advancedModelManager.getDetailedMemoryStats();
    if (stats.loadedModels < 3) {
      await advancedModelManager.preloadEssentialModels();
    }
    
    // تشغيل تحسين سريع
    await modelOptimizer.runManualOptimization(['performance_tuning']);
    
    console.log('✅ تم تحسين النظام للمعالجة');
  }
}

// الاستخدام
const memoryManager = new SmartMemoryManager();
memoryManager.startMonitoring();
```

### 5. **نظام إعادة المحاولة الذكي**

```typescript
export class SmartRetrySystem {
  private maxRetries = 3;
  private baseDelay = 1000; // 1 ثانية
  
  async processWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`🔄 ${operationName} - المحاولة ${attempt}/${this.maxRetries}`);
        
        // تحسين النظام قبل المحاولة
        if (attempt > 1) {
          await this.optimizeBeforeRetry(attempt);
        }
        
        const result = await operation();
        
        if (attempt > 1) {
          console.log(`✅ ${operationName} نجح في المحاولة ${attempt}`);
        }
        
        return result;
        
      } catch (error) {
        lastError = error as Error;
        console.warn(`⚠️ ${operationName} فشل في المحاولة ${attempt}:`, error);
        
        if (attempt < this.maxRetries) {
          const delay = this.calculateDelay(attempt);
          console.log(`⏳ انتظار ${delay}ms قبل المحاولة التالية...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    console.error(`❌ ${operationName} فشل نهائياً بعد ${this.maxRetries} محاولات`);
    throw lastError!;
  }
  
  private async optimizeBeforeRetry(attempt: number) {
    console.log(`🔧 تحسين النظام قبل المحاولة ${attempt}...`);
    
    try {
      if (attempt === 2) {
        // تحسين خفيف
        await modelOptimizer.runManualOptimization(['cache_optimization']);
      } else if (attempt === 3) {
        // تحسين قوي
        await modelOptimizer.runManualOptimization([
          'memory_cleanup',
          'garbage_collection'
        ]);
      }
    } catch (error) {
      console.warn('⚠️ فشل في التحسين:', error);
    }
  }
  
  private calculateDelay(attempt: number): number {
    // تأخير متزايد: 1s, 2s, 4s
    return this.baseDelay * Math.pow(2, attempt - 1);
  }
}

// الاستخدام
const retrySystem = new SmartRetrySystem();

const result = await retrySystem.processWithRetry(
  () => processSimpleDocument(file, userType),
  'معالجة المستند'
);
```

### 6. **مراقبة الأداء المتقدمة**

```typescript
export class PerformanceTracker {
  private metrics: Map<string, number[]> = new Map();
  private memorySnapshots: Array<{ timestamp: number; usage: number }> = [];
  
  startTracking() {
    // مراقبة الذاكرة كل 5 ثواني
    setInterval(() => {
      this.recordMemorySnapshot();
    }, 5000);
    
    console.log('📊 بدء مراقبة الأداء');
  }
  
  async measureOperation<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> {
    const startTime = Date.now();
    const startMemory = this.getCurrentMemoryUsage();
    
    try {
      const result = await operation();
      const duration = Date.now() - startTime;
      const endMemory = this.getCurrentMemoryUsage();
      
      this.recordMetric(operationName, duration);
      
      console.log(`⏱️ ${operationName}:`, {
        duration: `${duration}ms`,
        memoryChange: `${((endMemory - startMemory) / 1024 / 1024).toFixed(2)}MB`,
        avgDuration: `${this.getAverageMetric(operationName).toFixed(0)}ms`
      });
      
      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ ${operationName} فشل بعد ${duration}ms:`, error);
      throw error;
    }
  }
  
  private recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // الاحتفاظ بآخر 100 قياس فقط
    if (values.length > 100) {
      values.shift();
    }
  }
  
  private getAverageMetric(name: string): number {
    const values = this.metrics.get(name) || [];
    if (values.length === 0) return 0;
    
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }
  
  private getCurrentMemoryUsage(): number {
    return (performance as any).memory?.usedJSHeapSize || 0;
  }
  
  private recordMemorySnapshot() {
    this.memorySnapshots.push({
      timestamp: Date.now(),
      usage: this.getCurrentMemoryUsage()
    });
    
    // الاحتفاظ بآخر 100 لقطة
    if (this.memorySnapshots.length > 100) {
      this.memorySnapshots.shift();
    }
  }
  
  generateReport(): PerformanceReport {
    const report: PerformanceReport = {
      timestamp: Date.now(),
      operations: {},
      memoryTrend: this.analyzeMemoryTrend(),
      recommendations: this.generateRecommendations()
    };
    
    // تحليل العمليات
    for (const [name, values] of this.metrics.entries()) {
      report.operations[name] = {
        count: values.length,
        average: this.getAverageMetric(name),
        min: Math.min(...values),
        max: Math.max(...values),
        latest: values[values.length - 1] || 0
      };
    }
    
    return report;
  }
  
  private analyzeMemoryTrend(): 'increasing' | 'decreasing' | 'stable' {
    if (this.memorySnapshots.length < 10) return 'stable';
    
    const recent = this.memorySnapshots.slice(-5);
    const older = this.memorySnapshots.slice(-10, -5);
    
    const recentAvg = recent.reduce((sum, s) => sum + s.usage, 0) / recent.length;
    const olderAvg = older.reduce((sum, s) => sum + s.usage, 0) / older.length;
    
    if (recentAvg > olderAvg * 1.1) return 'increasing';
    if (recentAvg < olderAvg * 0.9) return 'decreasing';
    return 'stable';
  }
  
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const report = this.generateReport();
    
    // توصيات بناءً على أداء العمليات
    for (const [name, stats] of Object.entries(report.operations)) {
      if (stats.average > 10000) { // أكثر من 10 ثواني
        recommendations.push(`عملية ${name} بطيئة - متوسط ${stats.average.toFixed(0)}ms`);
      }
    }
    
    // توصيات بناءً على الذاكرة
    if (report.memoryTrend === 'increasing') {
      recommendations.push('استخدام الذاكرة في تزايد - فكر في تشغيل التحسين');
    }
    
    return recommendations;
  }
}

interface PerformanceReport {
  timestamp: number;
  operations: Record<string, {
    count: number;
    average: number;
    min: number;
    max: number;
    latest: number;
  }>;
  memoryTrend: 'increasing' | 'decreasing' | 'stable';
  recommendations: string[];
}

// الاستخدام
const performanceTracker = new PerformanceTracker();
performanceTracker.startTracking();

// قياس عملية
const result = await performanceTracker.measureOperation(
  () => processSimpleDocument(file, userType),
  'معالجة مستند الهوية'
);

// تقرير الأداء
const report = performanceTracker.generateReport();
console.log('📊 تقرير الأداء:', report);
```

## 🎯 سيناريوهات الاستخدام

### 7. **تطبيق كامل للتجار**

```typescript
export class MerchantDocumentProcessor {
  private memoryManager = new SmartMemoryManager();
  private retrySystem = new SmartRetrySystem();
  private performanceTracker = new PerformanceTracker();
  
  async initialize() {
    console.log('🏪 تهيئة معالج مستندات التجار...');
    
    // تهيئة النظام
    await initializeLocalAI();
    
    // بدء المراقبة
    this.memoryManager.startMonitoring();
    this.performanceTracker.startTracking();
    
    console.log('✅ معالج التجار جاهز!');
  }
  
  async processIdentityDocument(file: File): Promise<IdentityProcessingResult> {
    return this.performanceTracker.measureOperation(async () => {
      return this.retrySystem.processWithRetry(async () => {
        const result = await processSimpleDocument(file, 'merchant');
        
        // التحقق من متطلبات التجار
        if (result.classificationResult.documentType !== 'identity') {
          throw new Error('نوع المستند غير صحيح - مطلوب مستند هوية');
        }
        
        if (result.overallConfidence < 0.8) {
          throw new Error('مستوى الثقة منخفض - مطلوب مراجعة يدوية');
        }
        
        return {
          success: true,
          decision: result.classificationResult.decision,
          confidence: result.overallConfidence,
          extractedData: result.nerResult.extractedData,
          processingTime: result.processingTime
        };
      }, 'معالجة مستند الهوية');
    }, 'معالجة هوية تاجر');
  }
  
  async processBusinessRegistration(file: File): Promise<BusinessProcessingResult> {
    return this.performanceTracker.measureOperation(async () => {
      return this.retrySystem.processWithRetry(async () => {
        const result = await processSimpleDocument(file, 'merchant');
        
        // التحقق من متطلبات السجل التجاري
        if (result.classificationResult.documentType !== 'business') {
          throw new Error('نوع المستند غير صحيح - مطلوب سجل تجاري');
        }
        
        const requiredFields = ['registrationNumber', 'businessName', 'ownerName'];
        const missingFields = requiredFields.filter(
          field => !result.nerResult.extractedData[field]
        );
        
        if (missingFields.length > 0) {
          throw new Error(`حقول مفقودة: ${missingFields.join(', ')}`);
        }
        
        return {
          success: true,
          decision: result.classificationResult.decision,
          confidence: result.overallConfidence,
          businessData: result.nerResult.extractedData,
          processingTime: result.processingTime
        };
      }, 'معالجة السجل التجاري');
    }, 'معالجة سجل تجاري');
  }
  
  async generateDailyReport(): Promise<DailyReport> {
    const performanceReport = this.performanceTracker.generateReport();
    const memoryStats = advancedModelManager.getDetailedMemoryStats();
    const optimizationReport = modelOptimizer.getOptimizationReport();
    
    return {
      date: new Date().toISOString().split('T')[0],
      performance: performanceReport,
      memory: {
        currentUsage: memoryStats.totalUsage,
        peakUsage: Math.max(...memoryStats.modelBreakdown.map(m => m.memoryUsage)),
        efficiency: (memoryStats.availableMemory / memoryStats.maxUsage) * 100
      },
      optimization: {
        totalOptimizations: optimizationReport.totalOptimizations,
        successRate: optimizationReport.successRate,
        memoryFreed: optimizationReport.totalMemoryFreed
      },
      recommendations: [
        ...performanceReport.recommendations,
        ...memoryStats.recommendations,
        ...optimizationReport.recommendations
      ]
    };
  }
}

// أنواع البيانات
interface IdentityProcessingResult {
  success: boolean;
  decision: string;
  confidence: number;
  extractedData: Record<string, any>;
  processingTime: number;
}

interface BusinessProcessingResult {
  success: boolean;
  decision: string;
  confidence: number;
  businessData: Record<string, any>;
  processingTime: number;
}

interface DailyReport {
  date: string;
  performance: PerformanceReport;
  memory: {
    currentUsage: number;
    peakUsage: number;
    efficiency: number;
  };
  optimization: {
    totalOptimizations: number;
    successRate: number;
    memoryFreed: number;
  };
  recommendations: string[];
}

// الاستخدام
const merchantProcessor = new MerchantDocumentProcessor();
await merchantProcessor.initialize();

// معالجة مستندات
const identityResult = await merchantProcessor.processIdentityDocument(identityFile);
const businessResult = await merchantProcessor.processBusinessRegistration(businessFile);

// تقرير يومي
const dailyReport = await merchantProcessor.generateDailyReport();
console.log('📊 التقرير اليومي:', dailyReport);
```

## 🎉 الخلاصة

هذه الأمثلة توضح كيفية:
- **🚀 إعداد النظام** بشكل صحيح
- **📄 معالجة المستندات** بكفاءة
- **🧠 إدارة الذاكرة** بذكاء
- **⚡ تحسين الأداء** تلقائياً
- **📊 مراقبة النظام** باستمرار
- **🔄 التعامل مع الأخطاء** بمرونة

**النظام المحلي يوفر حلولاً متقدمة وموثوقة لجميع احتياجات معالجة المستندات!** 🎯
