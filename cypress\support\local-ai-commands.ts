// cypress/support/local-ai-commands.ts - أوامر مخصصة للنظام المحلي

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * تحميل النماذج المحلية مسبقاً
       */
      preloadLocalModels(): Chainable<void>;
      
      /**
       * التحقق من حالة النماذج المحلية
       */
      checkModelsStatus(): Chainable<any>;
      
      /**
       * مراقبة استخدام الذاكرة
       */
      monitorMemoryUsage(): Chainable<any>;
      
      /**
       * التحقق من المعالجة المحلية
       */
      verifyLocalProcessing(): Chainable<void>;
      
      /**
       * محاكاة مستند للاختبار
       */
      createTestDocument(type: 'identity' | 'business' | 'financial'): Chainable<File>;
      
      /**
       * انتظار اكتمال المعالجة المحلية
       */
      waitForLocalProcessing(timeout?: number): Chainable<void>;
      
      /**
       * التحقق من عدم وجود طلبات API خارجية
       */
      verifyNoExternalAPICalls(): Chainable<void>;
      
      /**
       * قياس أداء المعالجة
       */
      measureProcessingPerformance(): Chainable<any>;
    }
  }
}

/**
 * تحميل النماذج المحلية مسبقاً
 */
Cypress.Commands.add('preloadLocalModels', () => {
  cy.get('[data-testid="models-tab"]').click();
  cy.get('[data-testid="preload-models-btn"]').click();
  
  // انتظار تحميل النماذج الأساسية
  cy.get('[data-testid="model-status-ready"]', { timeout: 120000 })
    .should('have.length.at.least', 3);
  
  cy.log('✅ تم تحميل النماذج المحلية بنجاح');
});

/**
 * التحقق من حالة النماذج المحلية
 */
Cypress.Commands.add('checkModelsStatus', () => {
  return cy.request('GET', '/api/ai-document-processing/models-status')
    .then((response) => {
      expect(response.status).to.equal(200);
      expect(response.body.success).to.be.true;
      
      const models = response.body.data.models;
      const systemStats = response.body.data.systemStats;
      
      cy.log(`📊 حالة النماذج: ${models.length} نموذج، ${systemStats.loadedModelsCount} محمل`);
      
      return {
        models,
        systemStats,
        capabilities: response.body.data.capabilities
      };
    });
});

/**
 * مراقبة استخدام الذاكرة
 */
Cypress.Commands.add('monitorMemoryUsage', () => {
  return cy.window().then((win) => {
    const memory = (win.performance as any).memory;
    
    if (memory) {
      const memoryInfo = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
        usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
      };
      
      cy.log(`🧠 استخدام الذاكرة: ${(memoryInfo.usagePercentage).toFixed(1)}%`);
      
      return memoryInfo;
    } else {
      cy.log('⚠️ معلومات الذاكرة غير متاحة');
      return null;
    }
  });
});

/**
 * التحقق من المعالجة المحلية
 */
Cypress.Commands.add('verifyLocalProcessing', () => {
  // التحقق من وجود مؤشرات المعالجة المحلية
  cy.get('[data-testid="local-processing-indicator"]').should('be.visible');
  cy.contains('معالجة محلية آمنة').should('be.visible');
  cy.contains('بدون تكاليف إضافية').should('be.visible');
  
  // التحقق من عدم وجود مؤشرات المعالجة الخارجية
  cy.get('[data-testid="external-api-indicator"]').should('not.exist');
  cy.contains('معالجة عبر الخادم').should('not.exist');
  
  cy.log('✅ تم التحقق من المعالجة المحلية');
});

/**
 * محاكاة مستند للاختبار
 */
Cypress.Commands.add('createTestDocument', (type: 'identity' | 'business' | 'financial') => {
  const documentTemplates = {
    identity: {
      content: 'رقم الهوية: 1234567890\nالاسم: أحمد محمد علي\nتاريخ الميلاد: 15/03/1990\nتاريخ الإصدار: 01/01/2020\nتاريخ الانتهاء: 01/01/2030',
      filename: 'test-identity.jpg'
    },
    business: {
      content: 'رقم السجل التجاري: 1010123456\nاسم المنشأة: شركة التجارة المحدودة\nاسم المالك: محمد أحمد\nتاريخ الإصدار: 01/06/2023\nتاريخ الانتهاء: 01/06/2024',
      filename: 'test-business.jpg'
    },
    financial: {
      content: 'كشف حساب بنكي\nرقم الحساب: 123456789\nاسم العميل: سارة محمد\nالرصيد: 15,000 ريال\nتاريخ الكشف: 01/12/2023',
      filename: 'test-financial.jpg'
    }
  };
  
  const template = documentTemplates[type];
  const file = new File([template.content], template.filename, {
    type: 'image/jpeg'
  });
  
  cy.log(`📄 تم إنشاء مستند اختبار: ${type}`);
  
  return cy.wrap(file);
});

/**
 * انتظار اكتمال المعالجة المحلية
 */
Cypress.Commands.add('waitForLocalProcessing', (timeout = 90000) => {
  // انتظار بدء المعالجة
  cy.get('[data-testid="processing-started"]', { timeout: 5000 }).should('be.visible');
  
  // انتظار مراحل المعالجة
  cy.get('[data-testid="ocr-stage"]').should('be.visible');
  cy.get('[data-testid="ner-stage"]', { timeout: 30000 }).should('be.visible');
  cy.get('[data-testid="classification-stage"]', { timeout: 60000 }).should('be.visible');
  
  // انتظار اكتمال المعالجة
  cy.get('[data-testid="processing-complete"]', { timeout }).should('be.visible');
  
  cy.log('✅ اكتملت المعالجة المحلية');
});

/**
 * التحقق من عدم وجود طلبات API خارجية
 */
Cypress.Commands.add('verifyNoExternalAPICalls', () => {
  cy.window().then((win) => {
    // التحقق من عدم وجود طلبات للخوادم الخارجية
    const externalDomains = [
      'api.huggingface.co',
      'api.openai.com',
      'generativelanguage.googleapis.com',
      'api.anthropic.com'
    ];
    
    // في التطبيق الحقيقي، سيتم مراقبة طلبات الشبكة
    // هنا نتحقق من عدم وجود مؤشرات للطلبات الخارجية
    cy.get('[data-testid="external-api-calls"]').should('not.exist');
    cy.get('[data-testid="api-cost-indicator"]').should('not.exist');
    
    cy.log('✅ لا توجد طلبات API خارجية');
  });
});

/**
 * قياس أداء المعالجة
 */
Cypress.Commands.add('measureProcessingPerformance', () => {
  const startTime = Date.now();
  let memoryBefore: any;
  let memoryAfter: any;
  
  // قياس الذاكرة قبل المعالجة
  cy.monitorMemoryUsage().then((memory) => {
    memoryBefore = memory;
  });
  
  // انتظار اكتمال المعالجة
  cy.waitForLocalProcessing().then(() => {
    const endTime = Date.now();
    const processingTime = endTime - startTime;
    
    // قياس الذاكرة بعد المعالجة
    cy.monitorMemoryUsage().then((memory) => {
      memoryAfter = memory;
      
      const performanceMetrics = {
        processingTime,
        memoryUsage: {
          before: memoryBefore,
          after: memoryAfter,
          increase: memoryAfter ? memoryAfter.used - memoryBefore.used : 0
        },
        efficiency: {
          timePerMB: processingTime / (memoryAfter?.used || 1),
          memoryEfficiency: memoryBefore ? (memoryBefore.used / memoryAfter.used) * 100 : 100
        }
      };
      
      cy.log(`⚡ أداء المعالجة:`);
      cy.log(`  - الوقت: ${processingTime}ms`);
      cy.log(`  - زيادة الذاكرة: ${(performanceMetrics.memoryUsage.increase / 1024 / 1024).toFixed(2)}MB`);
      
      return cy.wrap(performanceMetrics);
    });
  });
});

// إضافة أوامر مساعدة للاختبارات

/**
 * تنظيف النماذج المحملة
 */
Cypress.Commands.add('cleanupModels', () => {
  cy.get('[data-testid="models-tab"]').click();
  
  // إلغاء تحميل جميع النماذج
  cy.get('[data-testid="unload-all-models-btn"]').click();
  
  // التحقق من تحرير الذاكرة
  cy.get('[data-testid="models-unloaded"]').should('be.visible');
  
  cy.log('🧹 تم تنظيف النماذج');
});

/**
 * إعادة تعيين حالة النظام
 */
Cypress.Commands.add('resetSystemState', () => {
  // تنظيف النماذج
  cy.cleanupModels();
  
  // إعادة تعيين الإعدادات
  cy.get('[data-testid="settings-tab"]').click();
  cy.get('[data-testid="reset-settings-btn"]').click();
  
  // تنظيف الكاش
  cy.clearLocalStorage();
  cy.clearCookies();
  
  cy.log('🔄 تم إعادة تعيين حالة النظام');
});

/**
 * التحقق من جودة النتائج
 */
Cypress.Commands.add('verifyResultsQuality', (expectedType: string) => {
  // التحقق من وجود النتائج
  cy.get('[data-testid="final-results"]').should('be.visible');
  
  // التحقق من نوع المستند
  cy.get('[data-testid="document-type"]').should('contain', expectedType);
  
  // التحقق من مستوى الثقة
  cy.get('[data-testid="confidence-score"]').then(($el) => {
    const confidence = parseFloat($el.text());
    expect(confidence).to.be.greaterThan(0.6); // ثقة أعلى من 60%
  });
  
  // التحقق من وجود البيانات المستخرجة
  cy.get('[data-testid="extracted-data"]').should('not.be.empty');
  
  cy.log(`✅ تم التحقق من جودة النتائج لنوع: ${expectedType}`);
});

export {};
