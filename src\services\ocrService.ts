// src/services/ocrService.ts - خدمة OCR المتقدمة باستخدام Hugging Face
import { OCRResult } from './huggingFaceAIService';

// إعدادات نماذج OCR
const OCR_MODELS = {
  // نماذج للنصوص المطبوعة
  printed: {
    primary: 'microsoft/trocr-base-printed',
    arabic: 'microsoft/trocr-base-arabic', // إذا كان متوفراً
    fallback: 'Salesforce/blip-image-captioning-base'
  },
  
  // نماذج للنصوص المكتوبة بخط اليد
  handwritten: {
    primary: 'microsoft/trocr-base-handwritten',
    fallback: 'microsoft/trocr-base-printed'
  },
  
  // نماذج للمستندات المعقدة
  document: {
    primary: 'microsoft/layoutlm-base-uncased',
    fallback: 'microsoft/trocr-base-printed'
  }
};

// إعدادات الجودة والتحسين
const QUALITY_SETTINGS = {
  minConfidence: 0.6,
  maxRetries: 3,
  timeoutMs: 30000,
  
  // إعدادات معالجة الصور
  imageProcessing: {
    maxWidth: 2048,
    maxHeight: 2048,
    quality: 0.9,
    format: 'jpeg'
  }
};

export class OCRService {
  private static instance: OCRService;
  private apiKey: string;
  private baseUrl: string;

  private constructor() {
    this.apiKey = process.env.HUGGING_FACE_API_KEY || '';
    this.baseUrl = 'https://api-inference.huggingface.co/models';
    
    if (!this.apiKey) {
      console.warn('⚠️ Hugging Face API key not found. OCR service will use fallback methods.');
    }
  }

  static getInstance(): OCRService {
    if (!OCRService.instance) {
      OCRService.instance = new OCRService();
    }
    return OCRService.instance;
  }

  /**
   * استخراج النص من الصورة أو المستند
   */
  async extractText(
    imageUrl: string, 
    mimeType: string,
    documentType?: string
  ): Promise<OCRResult> {
    const startTime = Date.now();
    
    try {
      // تحديد نوع المعالجة المطلوبة
      const processingType = this.determineProcessingType(mimeType, documentType);
      
      // معالجة الصورة إذا لزم الأمر
      const processedImageUrl = await this.preprocessImage(imageUrl, mimeType);
      
      // تجربة النماذج بالترتيب
      const models = this.getModelsForType(processingType);
      let bestResult: OCRResult | null = null;
      let lastError: Error | null = null;

      for (const modelName of models) {
        try {
          console.log(`🔍 جاري تجربة نموذج: ${modelName}`);
          
          const result = await this.callHuggingFaceOCR(processedImageUrl, modelName);
          
          if (result.confidence >= QUALITY_SETTINGS.minConfidence) {
            console.log(`✅ نجح النموذج ${modelName} بثقة ${result.confidence}`);
            return {
              ...result,
              processingTime: Date.now() - startTime,
              modelUsed: modelName
            };
          }
          
          // حفظ أفضل نتيجة حتى لو كانت أقل من الحد الأدنى
          if (!bestResult || result.confidence > bestResult.confidence) {
            bestResult = result;
          }
          
        } catch (error) {
          console.warn(`⚠️ فشل النموذج ${modelName}:`, error);
          lastError = error as Error;
          continue;
        }
      }

      // إذا لم ينجح أي نموذج بالحد الأدنى، استخدم أفضل نتيجة
      if (bestResult) {
        console.log(`📝 استخدام أفضل نتيجة متاحة بثقة ${bestResult.confidence}`);
        return {
          ...bestResult,
          processingTime: Date.now() - startTime,
          modelUsed: bestResult.modelUsed
        };
      }

      // إذا فشلت جميع النماذج، استخدم الطريقة البديلة
      console.log('🔄 جاري استخدام الطريقة البديلة...');
      return await this.fallbackOCR(imageUrl, mimeType, startTime);

    } catch (error) {
      console.error('❌ خطأ في استخراج النص:', error);
      throw new Error(`فشل في استخراج النص: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
    }
  }

  /**
   * تحديد نوع المعالجة المطلوبة
   */
  private determineProcessingType(mimeType: string, documentType?: string): 'printed' | 'handwritten' | 'document' {
    if (mimeType === 'application/pdf') {
      return 'document';
    }
    
    if (documentType?.includes('handwritten') || documentType?.includes('signature')) {
      return 'handwritten';
    }
    
    return 'printed';
  }

  /**
   * الحصول على قائمة النماذج للنوع المحدد
   */
  private getModelsForType(type: 'printed' | 'handwritten' | 'document'): string[] {
    const modelSet = OCR_MODELS[type];
    return [modelSet.primary, modelSet.fallback].filter(Boolean);
  }

  /**
   * معالجة الصورة مسبقاً
   */
  private async preprocessImage(imageUrl: string, mimeType: string): Promise<string> {
    // في التطبيق الحقيقي، يمكن تطبيق معالجة الصور هنا
    // مثل تحسين الجودة، إزالة الضوضاء، تصحيح الاتجاه
    return imageUrl;
  }

  /**
   * استدعاء Hugging Face OCR API
   */
  private async callHuggingFaceOCR(imageUrl: string, modelName: string): Promise<OCRResult> {
    const response = await fetch(`${this.baseUrl}/${modelName}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        inputs: imageUrl,
        options: {
          wait_for_model: true,
          use_cache: false
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OCR API Error (${response.status}): ${errorText}`);
    }

    const result = await response.json();
    
    // معالجة النتيجة حسب نوع النموذج
    let extractedText = '';
    let confidence = 0;

    if (Array.isArray(result)) {
      // نتيجة من نموذج TrOCR
      extractedText = result[0]?.generated_text || '';
      confidence = result[0]?.score || 0.8; // قيمة افتراضية
    } else if (result.generated_text) {
      // نتيجة مباشرة
      extractedText = result.generated_text;
      confidence = result.score || 0.8;
    } else {
      throw new Error('تنسيق نتيجة غير متوقع من API');
    }

    // تحديد اللغة
    const language = this.detectLanguage(extractedText);

    return {
      extractedText: extractedText.trim(),
      confidence,
      language,
      processingTime: 0, // سيتم حسابها في الدالة الرئيسية
      modelUsed: modelName
    };
  }

  /**
   * طريقة بديلة للـ OCR (Tesseract.js أو خدمة أخرى)
   */
  private async fallbackOCR(imageUrl: string, mimeType: string, startTime: number): Promise<OCRResult> {
    try {
      // يمكن استخدام Tesseract.js كبديل
      // const { createWorker } = await import('tesseract.js');
      // const worker = await createWorker();
      // await worker.loadLanguage('ara+eng');
      // await worker.initialize('ara+eng');
      // const { data: { text, confidence } } = await worker.recognize(imageUrl);
      // await worker.terminate();

      // محاكاة للتطوير
      const mockText = 'نص تجريبي مستخرج بالطريقة البديلة';
      const language = this.detectLanguage(mockText);

      return {
        extractedText: mockText,
        confidence: 0.7,
        language,
        processingTime: Date.now() - startTime,
        modelUsed: 'fallback-tesseract'
      };

    } catch (error) {
      console.error('❌ فشلت الطريقة البديلة أيضاً:', error);
      throw new Error('فشل في جميع طرق استخراج النص');
    }
  }

  /**
   * تحديد لغة النص
   */
  private detectLanguage(text: string): 'ar' | 'en' | 'mixed' {
    const arabicRegex = /[\u0600-\u06FF]/;
    const englishRegex = /[a-zA-Z]/;
    
    const hasArabic = arabicRegex.test(text);
    const hasEnglish = englishRegex.test(text);
    
    if (hasArabic && hasEnglish) return 'mixed';
    if (hasArabic) return 'ar';
    return 'en';
  }

  /**
   * فحص جودة النص المستخرج
   */
  async validateExtractedText(text: string): Promise<{
    isValid: boolean;
    quality: number;
    issues: string[];
  }> {
    const issues: string[] = [];
    let quality = 1.0;

    // فحص الطول
    if (text.length < 10) {
      issues.push('النص قصير جداً');
      quality -= 0.3;
    }

    // فحص الأحرف الغريبة
    const strangeCharsRegex = /[^\u0600-\u06FF\u0020-\u007E\s\d]/g;
    const strangeChars = text.match(strangeCharsRegex);
    if (strangeChars && strangeChars.length > text.length * 0.1) {
      issues.push('يحتوي على أحرف غريبة كثيرة');
      quality -= 0.2;
    }

    // فحص التكرار المفرط
    const words = text.split(/\s+/);
    const uniqueWords = new Set(words);
    if (words.length > 0 && uniqueWords.size / words.length < 0.3) {
      issues.push('تكرار مفرط في الكلمات');
      quality -= 0.2;
    }

    return {
      isValid: quality >= 0.5,
      quality: Math.max(0, quality),
      issues
    };
  }
}
