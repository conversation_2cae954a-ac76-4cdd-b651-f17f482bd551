// src/services/localOCRService.ts - خدمة OCR المحلية المتقدمة باستخدام Transformers.js

import { modelManager } from './localModelManager';
import { OCRResult } from './huggingFaceAIService';
import { 
  TRANSFORMERS_CONFIG, 
  SupportedLanguage,
  OCR_MODELS 
} from '../config/transformersConfig';

// إعدادات معالجة الصور
const IMAGE_PROCESSING_CONFIG = {
  maxWidth: 2048,
  maxHeight: 2048,
  quality: 0.9,
  format: 'image/jpeg' as const,
  enablePreprocessing: true
};

// إعدادات كشف اللغة
const LANGUAGE_PATTERNS = {
  arabic: /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]/,
  english: /[a-zA-Z]/,
  numbers: /[0-9\u0660-\u0669]/
};

/**
 * خدمة OCR المحلية المحسّنة للأداء والدقة
 */
export class LocalOCRService {
  private static instance: LocalOCRService;

  private constructor() {}

  static getInstance(): LocalOCRService {
    if (!LocalOCRService.instance) {
      LocalOCRService.instance = new LocalOCRService();
    }
    return LocalOCRService.instance;
  }

  /**
   * استخراج النص من الصورة باستخدام النماذج المحلية
   */
  async extractText(
    imageUrl: string, 
    mimeType: string,
    onProgress?: (progress: { stage: string; percentage: number }) => void
  ): Promise<OCRResult> {
    const startTime = Date.now();
    
    try {
      // المرحلة 1: تحضير الصورة
      onProgress?.({ stage: 'تحضير الصورة', percentage: 10 });
      const processedImage = await this.preprocessImage(imageUrl, mimeType);
      
      // المرحلة 2: كشف نوع النص (مطبوع/يدوي)
      onProgress?.({ stage: 'تحليل نوع النص', percentage: 20 });
      const textType = await this.detectTextType(processedImage);
      
      // المرحلة 3: اختيار النموذج المناسب
      const modelId = this.selectBestModel(textType);
      
      // المرحلة 4: تحميل النموذج
      onProgress?.({ stage: 'تحميل نموذج OCR', percentage: 30 });
      const pipeline = await modelManager.loadModel(
        modelId, 
        'ocr',
        (progress) => {
          const adjustedPercentage = 30 + (progress.progress * 0.4); // 30-70%
          onProgress?.({ stage: `تحميل النموذج: ${progress.status}`, percentage: adjustedPercentage });
        }
      );
      
      // المرحلة 5: استخراج النص
      onProgress?.({ stage: 'استخراج النص', percentage: 70 });
      const extractedText = await this.performOCR(pipeline, processedImage);
      
      // المرحلة 6: معالجة وتنظيف النص
      onProgress?.({ stage: 'معالجة النص', percentage: 85 });
      const cleanedText = this.cleanExtractedText(extractedText);
      
      // المرحلة 7: كشف اللغة وحساب الثقة
      onProgress?.({ stage: 'تحليل النتائج', percentage: 95 });
      const language = this.detectLanguage(cleanedText);
      const confidence = this.calculateConfidence(cleanedText, extractedText);
      
      onProgress?.({ stage: 'اكتمل', percentage: 100 });
      
      const result: OCRResult = {
        extractedText: cleanedText,
        confidence,
        language,
        processingTime: Date.now() - startTime,
        modelUsed: modelId,
        metadata: {
          originalLength: extractedText.length,
          cleanedLength: cleanedText.length,
          textType,
          imageProcessed: true
        }
      };

      console.log(`✅ OCR مكتمل: ${cleanedText.length} حرف، ثقة: ${confidence}`);
      return result;

    } catch (error) {
      console.error('❌ خطأ في OCR المحلي:', error);
      
      // محاولة استخدام Tesseract.js كبديل
      return await this.fallbackToTesseract(imageUrl, mimeType);
    }
  }

  /**
   * معالجة مسبقة للصورة لتحسين دقة OCR
   */
  private async preprocessImage(imageUrl: string, mimeType: string): Promise<string> {
    if (!IMAGE_PROCESSING_CONFIG.enablePreprocessing) {
      return imageUrl;
    }

    try {
      // تحميل الصورة
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      
      // إنشاء canvas لمعالجة الصورة
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      return new Promise((resolve, reject) => {
        img.onload = () => {
          // تحديد الأبعاد المحسّنة
          const { width, height } = this.calculateOptimalDimensions(
            img.width, 
            img.height
          );
          
          canvas.width = width;
          canvas.height = height;
          
          // رسم الصورة مع التحسينات
          ctx!.drawImage(img, 0, 0, width, height);
          
          // تطبيق فلاتر التحسين
          this.applyImageEnhancements(ctx!, width, height);
          
          // تحويل إلى Data URL
          const processedDataUrl = canvas.toDataURL(
            IMAGE_PROCESSING_CONFIG.format,
            IMAGE_PROCESSING_CONFIG.quality
          );
          
          resolve(processedDataUrl);
        };
        
        img.onerror = () => reject(new Error('فشل في تحميل الصورة'));
        img.src = URL.createObjectURL(blob);
      });
      
    } catch (error) {
      console.warn('⚠️ فشل في معالجة الصورة، استخدام الأصلية:', error);
      return imageUrl;
    }
  }

  /**
   * كشف نوع النص (مطبوع أم يدوي)
   */
  private async detectTextType(imageUrl: string): Promise<'printed' | 'handwritten'> {
    // تحليل بسيط بناءً على خصائص الصورة
    // في التطبيق الحقيقي، يمكن استخدام نموذج متخصص
    return 'printed'; // افتراضي للنصوص المطبوعة
  }

  /**
   * اختيار أفضل نموذج OCR
   */
  private selectBestModel(textType: 'printed' | 'handwritten'): string {
    if (textType === 'handwritten') {
      return OCR_MODELS.trocr_handwritten.id;
    }
    return OCR_MODELS.trocr_printed.id;
  }

  /**
   * تنفيذ OCR باستخدام النموذج المحمل
   */
  private async performOCR(pipeline: any, imageUrl: string): Promise<string> {
    try {
      const result = await pipeline(imageUrl);
      
      // استخراج النص من النتيجة
      if (Array.isArray(result)) {
        return result.map(item => item.generated_text || item.text || '').join(' ');
      } else if (result.generated_text) {
        return result.generated_text;
      } else if (result.text) {
        return result.text;
      }
      
      return String(result);
      
    } catch (error) {
      console.error('❌ خطأ في تنفيذ OCR:', error);
      throw new Error(`فشل في استخراج النص: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
    }
  }

  /**
   * تنظيف وتحسين النص المستخرج
   */
  private cleanExtractedText(text: string): string {
    return text
      // إزالة المسافات الزائدة
      .replace(/\s+/g, ' ')
      // إزالة الأحرف الغريبة
      .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FFa-zA-Z0-9\u0660-\u0669\s\-_.,;:()\[\]]/g, '')
      // تنظيف بداية ونهاية النص
      .trim();
  }

  /**
   * كشف لغة النص
   */
  private detectLanguage(text: string): SupportedLanguage {
    const arabicMatches = (text.match(LANGUAGE_PATTERNS.arabic) || []).length;
    const englishMatches = (text.match(LANGUAGE_PATTERNS.english) || []).length;
    
    if (arabicMatches > englishMatches) {
      return 'ar';
    } else if (englishMatches > arabicMatches) {
      return 'en';
    } else {
      return 'mixed';
    }
  }

  /**
   * حساب مستوى الثقة
   */
  private calculateConfidence(cleanedText: string, originalText: string): number {
    // حساب الثقة بناءً على عوامل متعددة
    let confidence = 0.5; // قيمة أساسية
    
    // عامل طول النص
    if (cleanedText.length > 10) confidence += 0.2;
    if (cleanedText.length > 50) confidence += 0.1;
    
    // عامل نسبة التنظيف
    const cleaningRatio = cleanedText.length / Math.max(originalText.length, 1);
    confidence += cleaningRatio * 0.2;
    
    // عامل وجود كلمات مفهومة
    const wordCount = cleanedText.split(/\s+/).length;
    if (wordCount > 3) confidence += 0.1;
    
    return Math.min(confidence, 1.0);
  }

  /**
   * حساب الأبعاد المثلى للصورة
   */
  private calculateOptimalDimensions(originalWidth: number, originalHeight: number): { width: number; height: number } {
    const maxWidth = IMAGE_PROCESSING_CONFIG.maxWidth;
    const maxHeight = IMAGE_PROCESSING_CONFIG.maxHeight;
    
    let width = originalWidth;
    let height = originalHeight;
    
    // تقليل الحجم إذا كان كبيراً جداً
    if (width > maxWidth || height > maxHeight) {
      const ratio = Math.min(maxWidth / width, maxHeight / height);
      width = Math.round(width * ratio);
      height = Math.round(height * ratio);
    }
    
    return { width, height };
  }

  /**
   * تطبيق تحسينات على الصورة
   */
  private applyImageEnhancements(ctx: CanvasRenderingContext2D, width: number, height: number): void {
    // تحسين التباين والسطوع
    const imageData = ctx.getImageData(0, 0, width, height);
    const data = imageData.data;
    
    for (let i = 0; i < data.length; i += 4) {
      // تحسين التباين
      data[i] = Math.min(255, data[i] * 1.2);     // أحمر
      data[i + 1] = Math.min(255, data[i + 1] * 1.2); // أخضر
      data[i + 2] = Math.min(255, data[i + 2] * 1.2); // أزرق
    }
    
    ctx.putImageData(imageData, 0, 0);
  }

  /**
   * استخدام Tesseract.js كبديل في حالة الفشل
   */
  private async fallbackToTesseract(imageUrl: string, mimeType: string): Promise<OCRResult> {
    try {
      // استيراد Tesseract.js ديناميكياً
      const Tesseract = await import('tesseract.js');
      
      console.log('🔄 استخدام Tesseract.js كبديل...');
      
      const result = await Tesseract.recognize(imageUrl, 'ara+eng');
      
      return {
        extractedText: this.cleanExtractedText(result.data.text),
        confidence: result.data.confidence / 100,
        language: this.detectLanguage(result.data.text),
        processingTime: 0,
        modelUsed: 'tesseract.js',
        metadata: {
          fallback: true,
          tesseractConfidence: result.data.confidence
        }
      };
      
    } catch (error) {
      console.error('❌ فشل في البديل Tesseract.js:', error);
      throw new Error('فشل في جميع طرق استخراج النص');
    }
  }
}
