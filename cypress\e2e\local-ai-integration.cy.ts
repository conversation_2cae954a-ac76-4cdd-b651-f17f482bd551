// cypress/e2e/local-ai-integration.cy.ts - اختبارات التكامل للنظام المحلي

describe('🔗 تكامل النظام المحلي مع التطبيق', () => {
  let systemEvents: any[] = [];
  let apiCalls: any[] = [];

  beforeEach(() => {
    systemEvents = [];
    apiCalls = [];

    // مراقبة أحداث النظام
    cy.window().then((win) => {
      win.addEventListener('local-ai-event', (event: any) => {
        systemEvents.push({
          type: event.detail.type,
          data: event.detail.data,
          timestamp: Date.now()
        });
      });
    });

    // مراقبة استدعاءات API
    cy.intercept('POST', '/api/ai-document-processing/**', (req) => {
      apiCalls.push({
        url: req.url,
        body: req.body,
        timestamp: Date.now()
      });
      req.continue();
    });

    cy.login('merchant');
    cy.visit('/ai-document-processing');
  });

  describe('🔄 تكامل API المحلي', () => {
    it('يجب أن يستخدم نقاط النهاية المحدثة', () => {
      const testFile = new File(['test content'], 'test-document.jpg', {
        type: 'image/jpeg'
      });

      cy.get('[data-testid="file-input"]').selectFile(testFile, { force: true });
      cy.get('[data-testid="process-document-btn"]').click();

      // التحقق من استدعاء API المحلي
      cy.wait(2000).then(() => {
        const uploadCall = apiCalls.find(call => call.url.includes('/upload'));
        expect(uploadCall).to.exist;
        
        // التحقق من وجود معلومات المعالجة المحلية في الاستجابة
        cy.request('GET', `/api/ai-document-processing/status/${uploadCall.body.processingId}`)
          .then((response) => {
            expect(response.body.data.processingType).to.equal('local');
            expect(response.body.data.benefits).to.include('معالجة مجانية');
            expect(response.body.data.benefits).to.include('حماية الخصوصية');
          });
      });
    });

    it('يجب أن يعرض حالة النماذج المحلية عبر API', () => {
      cy.request('GET', '/api/ai-document-processing/models-status')
        .then((response) => {
          expect(response.status).to.equal(200);
          expect(response.body.success).to.be.true;
          
          // التحقق من بيانات النماذج
          expect(response.body.data.models).to.be.an('array');
          expect(response.body.data.systemStats).to.exist;
          expect(response.body.data.capabilities).to.exist;
          
          // التحقق من وجود النماذج الأساسية
          const models = response.body.data.models;
          const ocrModel = models.find((m: any) => m.task === 'ocr');
          const nerModel = models.find((m: any) => m.task === 'ner');
          const classificationModel = models.find((m: any) => m.task === 'classification');
          
          expect(ocrModel).to.exist;
          expect(nerModel).to.exist;
          expect(classificationModel).to.exist;
        });
    });

    it('يجب أن يدير النماذج عبر API', () => {
      // تحميل النماذج مسبقاً
      cy.request('POST', '/api/ai-document-processing/models-status', {
        action: 'preload'
      }).then((response) => {
        expect(response.status).to.equal(200);
        expect(response.body.success).to.be.true;
        expect(response.body.data.estimatedTime).to.exist;
        expect(response.body.data.modelsToLoad).to.be.an('array');
      });

      // تنظيف الذاكرة
      cy.request('POST', '/api/ai-document-processing/models-status', {
        action: 'cleanup'
      }).then((response) => {
        expect(response.status).to.equal(200);
        expect(response.body.success).to.be.true;
        expect(response.body.data.freedMemory).to.exist;
      });
    });
  });

  describe('🎯 تكامل المعالجة الكاملة', () => {
    it('يجب أن يكمل دورة المعالجة الكاملة', () => {
      const testFile = new File(['test content'], 'complete-test.jpg', {
        type: 'image/jpeg'
      });

      cy.get('[data-testid="file-input"]').selectFile(testFile, { force: true });
      cy.get('[data-testid="process-document-btn"]').click();

      // مراقبة تقدم المعالجة
      cy.get('[data-testid="processing-progress"]').should('be.visible');
      
      // التحقق من المراحل الثلاث
      cy.get('[data-testid="ocr-stage"]').should('be.visible');
      cy.get('[data-testid="ner-stage"]', { timeout: 30000 }).should('be.visible');
      cy.get('[data-testid="classification-stage"]', { timeout: 60000 }).should('be.visible');
      
      // انتظار اكتمال المعالجة
      cy.get('[data-testid="processing-complete"]', { timeout: 90000 }).should('be.visible');
      
      // التحقق من النتائج النهائية
      cy.get('[data-testid="final-results"]').should('be.visible');
      cy.get('[data-testid="processing-summary"]').should('contain', 'معالجة محلية');
      
      // التحقق من حفظ النتائج
      cy.get('[data-testid="processing-id"]').should('be.visible');
    });

    it('يجب أن يتعامل مع أنواع مستندات مختلفة', () => {
      const documentTypes = [
        { file: 'id-card.jpg', expectedType: 'identity' },
        { file: 'commercial-registration.jpg', expectedType: 'business' },
        { file: 'bank-statement.jpg', expectedType: 'financial' }
      ];

      documentTypes.forEach((doc, index) => {
        const testFile = new File(['test content'], doc.file, {
          type: 'image/jpeg'
        });

        cy.get('[data-testid="file-input"]').selectFile(testFile, { force: true });
        cy.get('[data-testid="process-document-btn"]').click();
        
        cy.get('[data-testid="processing-complete"]', { timeout: 90000 }).should('be.visible');
        
        // التحقق من تصنيف نوع المستند
        cy.get('[data-testid="document-type"]').should('contain', doc.expectedType);
        
        // إعادة تعيين للمستند التالي
        if (index < documentTypes.length - 1) {
          cy.get('[data-testid="new-document-btn"]').click();
        }
      });
    });

    it('يجب أن يحافظ على الأداء مع المعالجة المتتالية', () => {
      const files = [
        new File(['test1'], 'doc1.jpg', { type: 'image/jpeg' }),
        new File(['test2'], 'doc2.jpg', { type: 'image/jpeg' }),
        new File(['test3'], 'doc3.jpg', { type: 'image/jpeg' })
      ];

      const processingTimes: number[] = [];

      files.forEach((file, index) => {
        const startTime = Date.now();
        
        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="process-document-btn"]').click();
        
        cy.get('[data-testid="processing-complete"]', { timeout: 90000 })
          .should('be.visible')
          .then(() => {
            const processingTime = Date.now() - startTime;
            processingTimes.push(processingTime);
            
            // التحقق من أن الأداء لا يتدهور
            if (index > 0) {
              const previousTime = processingTimes[index - 1];
              const currentTime = processingTimes[index];
              
              // يجب ألا يزيد الوقت بأكثر من 50%
              expect(currentTime).to.be.lessThan(previousTime * 1.5);
            }
            
            cy.log(`معالجة المستند ${index + 1}: ${processingTime}ms`);
          });
        
        // إعداد للمستند التالي
        if (index < files.length - 1) {
          cy.get('[data-testid="new-document-btn"]').click();
        }
      });
    });
  });

  describe('🛡️ تكامل الأمان والخصوصية', () => {
    it('يجب ألا يرسل بيانات حساسة للخوادم الخارجية', () => {
      const sensitiveFile = new File(['sensitive data'], 'sensitive-id.jpg', {
        type: 'image/jpeg'
      });

      // مراقبة جميع طلبات الشبكة
      let externalRequests: any[] = [];
      
      cy.intercept('**/*', (req) => {
        if (!req.url.includes(window.location.origin)) {
          externalRequests.push({
            url: req.url,
            method: req.method,
            body: req.body
          });
        }
        req.continue();
      });

      cy.get('[data-testid="file-input"]').selectFile(sensitiveFile, { force: true });
      cy.get('[data-testid="process-document-btn"]').click();
      
      cy.get('[data-testid="processing-complete"]', { timeout: 90000 }).should('be.visible');
      
      // التحقق من عدم إرسال البيانات خارجياً
      cy.then(() => {
        const dataRequests = externalRequests.filter(req => 
          req.method === 'POST' && req.body
        );
        expect(dataRequests).to.have.length(0);
      });
    });

    it('يجب أن يعرض مؤشرات الأمان', () => {
      cy.get('[data-testid="security-indicators"]').should('be.visible');
      cy.contains('معالجة محلية آمنة').should('be.visible');
      cy.contains('حماية الخصوصية').should('be.visible');
      
      // التحقق من عدم وجود تحذيرات أمنية
      cy.get('[data-testid="security-warning"]').should('not.exist');
    });

    it('يجب أن يحذر من المشاكل الأمنية المحتملة', () => {
      // محاكاة مشكلة أمنية (مثل فشل في التحقق من النموذج)
      cy.window().then((win) => {
        win.dispatchEvent(new CustomEvent('security-issue', {
          detail: { type: 'model-verification-failed' }
        }));
      });

      // التحقق من ظهور التحذير
      cy.get('[data-testid="security-warning"]').should('be.visible');
      cy.contains('تحذير أمني').should('be.visible');
    });
  });

  describe('📊 تكامل التحليلات والمراقبة', () => {
    it('يجب أن يجمع إحصائيات الاستخدام', () => {
      const testFile = new File(['test content'], 'analytics-test.jpg', {
        type: 'image/jpeg'
      });

      cy.get('[data-testid="file-input"]').selectFile(testFile, { force: true });
      cy.get('[data-testid="process-document-btn"]').click();
      
      cy.get('[data-testid="processing-complete"]', { timeout: 90000 }).should('be.visible');
      
      // التحقق من تسجيل الإحصائيات
      cy.then(() => {
        const usageEvents = systemEvents.filter(event => 
          event.type === 'processing-complete'
        );
        expect(usageEvents).to.have.length.at.least(1);
        
        const event = usageEvents[0];
        expect(event.data.processingTime).to.exist;
        expect(event.data.modelUsed).to.exist;
        expect(event.data.confidence).to.exist;
      });
    });

    it('يجب أن يراقب أداء النماذج', () => {
      cy.get('[data-testid="models-tab"]').click();
      cy.get('[data-testid="preload-models-btn"]').click();
      
      cy.get('[data-testid="model-status-ready"]', { timeout: 120000 })
        .should('have.length.at.least', 3);
      
      // التحقق من تسجيل أحداث تحميل النماذج
      cy.then(() => {
        const modelEvents = systemEvents.filter(event => 
          event.type === 'model-loaded'
        );
        expect(modelEvents).to.have.length.at.least(3);
        
        modelEvents.forEach(event => {
          expect(event.data.modelId).to.exist;
          expect(event.data.loadTime).to.exist;
          expect(event.data.memoryUsage).to.exist;
        });
      });
    });

    it('يجب أن يولد تقارير الأداء', () => {
      cy.get('[data-testid="performance-tab"]').click();
      
      // في المستقبل عندما يتم تطوير تقارير الأداء
      cy.get('[data-testid="performance-report"]').should('be.visible');
      
      // التحقق من وجود مقاييس الأداء
      cy.get('[data-testid="avg-processing-time"]').should('be.visible');
      cy.get('[data-testid="success-rate"]').should('be.visible');
      cy.get('[data-testid="memory-efficiency"]').should('be.visible');
    });
  });

  describe('🔧 تكامل الإعدادات والتخصيص', () => {
    it('يجب أن يحفظ إعدادات المستخدم', () => {
      cy.get('[data-testid="settings-tab"]').click();
      
      // تغيير الإعدادات
      cy.get('[data-testid="auto-cleanup-toggle"]').click();
      cy.get('[data-testid="aggressive-mode-toggle"]').click();
      
      // إعادة تحميل الصفحة
      cy.reload();
      
      // التحقق من حفظ الإعدادات
      cy.get('[data-testid="settings-tab"]').click();
      cy.get('[data-testid="auto-cleanup-toggle"]').should('be.checked');
      cy.get('[data-testid="aggressive-mode-toggle"]').should('be.checked');
    });

    it('يجب أن يطبق إعدادات الأداء', () => {
      cy.get('[data-testid="settings-tab"]').click();
      
      // تفعيل الوضع العدواني
      cy.get('[data-testid="aggressive-mode-toggle"]').click();
      
      // اختبار تأثير الإعدادات
      const testFile = new File(['test content'], 'settings-test.jpg', {
        type: 'image/jpeg'
      });

      cy.get('[data-testid="upload-tab"]').click();
      cy.get('[data-testid="file-input"]').selectFile(testFile, { force: true });
      cy.get('[data-testid="process-document-btn"]').click();
      
      // التحقق من تطبيق الإعدادات
      cy.get('[data-testid="processing-mode"]').should('contain', 'aggressive');
    });
  });

  afterEach(() => {
    // تسجيل إحصائيات التكامل
    cy.then(() => {
      console.log('🔗 تقرير التكامل:');
      console.log(`- أحداث النظام: ${systemEvents.length}`);
      console.log(`- استدعاءات API: ${apiCalls.length}`);
      
      const processingEvents = systemEvents.filter(e => e.type === 'processing-complete');
      console.log(`- عمليات معالجة مكتملة: ${processingEvents.length}`);
      
      const modelEvents = systemEvents.filter(e => e.type === 'model-loaded');
      console.log(`- نماذج محملة: ${modelEvents.length}`);
      
      // حفظ تقرير التكامل
      cy.writeFile('cypress/reports/integration-report.json', {
        systemEvents,
        apiCalls,
        timestamp: Date.now()
      });
    });
  });
});
