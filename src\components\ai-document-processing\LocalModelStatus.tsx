'use client';

import React, { useState, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Brain, 
  Download, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  HardDrive,
  Zap,
  Shield
} from 'lucide-react';

interface ModelStatus {
  id: string;
  name: string;
  task: 'ocr' | 'ner' | 'classification';
  status: 'not_loaded' | 'downloading' | 'loading' | 'ready' | 'error';
  progress: number;
  size: string;
  error?: string;
}

interface MemoryStats {
  totalUsage: number;
  maxUsage: number;
  loadedModels: number;
  availableMemory: number;
}

interface LocalModelStatusProps {
  onPreloadModels?: () => Promise<void>;
  className?: string;
}

export default function LocalModelStatus({ 
  onPreloadModels, 
  className = '' 
}: LocalModelStatusProps) {
  const [models, setModels] = useState<ModelStatus[]>([
    {
      id: 'trocr-printed',
      name: 'TrOCR للنصوص المطبوعة',
      task: 'ocr',
      status: 'not_loaded',
      progress: 0,
      size: '~45MB'
    },
    {
      id: 'bert-multilingual',
      name: 'BERT متعدد اللغات',
      task: 'ner',
      status: 'not_loaded',
      progress: 0,
      size: '~110MB'
    },
    {
      id: 'distilbert-classifier',
      name: 'مصنف المستندات',
      task: 'classification',
      status: 'not_loaded',
      progress: 0,
      size: '~65MB'
    }
  ]);

  const [memoryStats, setMemoryStats] = useState<MemoryStats>({
    totalUsage: 0,
    maxUsage: 512 * 1024 * 1024, // 512MB
    loadedModels: 0,
    availableMemory: 512 * 1024 * 1024
  });

  const [isPreloading, setIsPreloading] = useState(false);

  // محاكاة تحديث حالة النماذج
  useEffect(() => {
    const interval = setInterval(() => {
      // في التطبيق الحقيقي، سيتم الحصول على هذه البيانات من modelManager
      updateMemoryStats();
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const updateMemoryStats = () => {
    // محاكاة إحصائيات الذاكرة
    const loadedCount = models.filter(m => m.status === 'ready').length;
    const totalUsage = loadedCount * 70 * 1024 * 1024; // تقدير 70MB لكل نموذج
    
    setMemoryStats({
      totalUsage,
      maxUsage: 512 * 1024 * 1024,
      loadedModels: loadedCount,
      availableMemory: (512 * 1024 * 1024) - totalUsage
    });
  };

  const handlePreloadModels = async () => {
    if (isPreloading) return;
    
    setIsPreloading(true);
    
    try {
      // محاكاة تحميل النماذج
      for (let i = 0; i < models.length; i++) {
        setModels(prev => prev.map((model, index) => 
          index === i 
            ? { ...model, status: 'downloading', progress: 0 }
            : model
        ));

        // محاكاة تقدم التحميل
        for (let progress = 0; progress <= 100; progress += 10) {
          await new Promise(resolve => setTimeout(resolve, 200));
          setModels(prev => prev.map((model, index) => 
            index === i 
              ? { ...model, progress, status: progress === 100 ? 'ready' : 'downloading' }
              : model
          ));
        }
      }

      if (onPreloadModels) {
        await onPreloadModels();
      }

    } catch (error) {
      console.error('خطأ في تحميل النماذج:', error);
      setModels(prev => prev.map(model => ({
        ...model,
        status: 'error',
        error: 'فشل في التحميل'
      })));
    } finally {
      setIsPreloading(false);
    }
  };

  const getStatusIcon = (status: ModelStatus['status']) => {
    switch (status) {
      case 'ready':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'downloading':
      case 'loading':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Download className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: ModelStatus['status']) => {
    switch (status) {
      case 'ready':
        return <Badge variant="default" className="bg-green-100 text-green-800">جاهز</Badge>;
      case 'downloading':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">يتم التحميل</Badge>;
      case 'loading':
        return <Badge variant="default" className="bg-yellow-100 text-yellow-800">يتم التحضير</Badge>;
      case 'error':
        return <Badge variant="destructive">خطأ</Badge>;
      default:
        return <Badge variant="outline">غير محمل</Badge>;
    }
  };

  const getTaskIcon = (task: ModelStatus['task']) => {
    switch (task) {
      case 'ocr':
        return '📄';
      case 'ner':
        return '🔍';
      case 'classification':
        return '📊';
      default:
        return '🤖';
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const memoryUsagePercentage = (memoryStats.totalUsage / memoryStats.maxUsage) * 100;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* بطاقة المزايا */}
      <Card className="border-green-200 bg-green-50">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-green-800">
            <Shield className="h-5 w-5" />
            المعالجة المحلية المجانية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-green-600" />
              <span>بدون تكاليف إضافية</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span>حماية خصوصية البيانات</span>
            </div>
            <div className="flex items-center gap-2">
              <Brain className="h-4 w-4 text-green-600" />
              <span>معالجة ذكية محلية</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* بطاقة حالة النماذج */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              حالة النماذج المحلية
            </CardTitle>
            <Button 
              onClick={handlePreloadModels}
              disabled={isPreloading}
              size="sm"
              variant="outline"
            >
              {isPreloading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  جاري التحميل...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  تحميل النماذج
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {models.map((model) => (
            <div key={model.id} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-lg">{getTaskIcon(model.task)}</span>
                  <div>
                    <div className="font-medium">{model.name}</div>
                    <div className="text-sm text-gray-500">{model.size}</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(model.status)}
                  {getStatusBadge(model.status)}
                </div>
              </div>
              
              {(model.status === 'downloading' || model.status === 'loading') && (
                <div className="space-y-1">
                  <Progress value={model.progress} className="h-2" />
                  <div className="text-xs text-gray-500 text-center">
                    {model.progress}%
                  </div>
                </div>
              )}
              
              {model.error && (
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {model.error}
                </div>
              )}
            </div>
          ))}
        </CardContent>
      </Card>

      {/* بطاقة إحصائيات الذاكرة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HardDrive className="h-5 w-5" />
            استخدام الذاكرة
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>المستخدم</span>
              <span>{formatBytes(memoryStats.totalUsage)} / {formatBytes(memoryStats.maxUsage)}</span>
            </div>
            <Progress value={memoryUsagePercentage} className="h-2" />
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-gray-500">النماذج المحملة</div>
              <div className="font-medium">{memoryStats.loadedModels}</div>
            </div>
            <div>
              <div className="text-gray-500">الذاكرة المتاحة</div>
              <div className="font-medium">{formatBytes(memoryStats.availableMemory)}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
