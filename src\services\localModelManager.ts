// src/services/localModelManager.ts - مدير النماذج المحلية المحسّن

import { pipeline, Pipeline, env } from '@xenova/transformers';
import { 
  ModelConfig, 
  TRANSFORMERS_CONFIG, 
  ModelTask, 
  SupportedLanguage 
} from '../config/transformersConfig';

// تكوين البيئة لـ Transformers.js
env.allowRemoteModels = true;
env.allowLocalModels = true;
env.useBrowserCache = true;

interface LoadedModel {
  pipeline: Pipeline;
  config: ModelConfig;
  loadedAt: number;
  lastUsed: number;
  memoryUsage: number;
}

interface ModelLoadProgress {
  modelId: string;
  progress: number;
  status: 'downloading' | 'loading' | 'ready' | 'error';
  error?: string;
}

/**
 * مدير النماذج المحلية - يدير تحميل وتخزين النماذج بكفاءة
 */
export class LocalModelManager {
  private static instance: LocalModelManager;
  private loadedModels: Map<string, LoadedModel> = new Map();
  private loadingPromises: Map<string, Promise<Pipeline>> = new Map();
  private progressCallbacks: Map<string, (progress: ModelLoadProgress) => void> = new Map();
  private memoryUsage: number = 0;

  private constructor() {
    this.setupMemoryMonitoring();
  }

  static getInstance(): LocalModelManager {
    if (!LocalModelManager.instance) {
      LocalModelManager.instance = new LocalModelManager();
    }
    return LocalModelManager.instance;
  }

  /**
   * تحميل نموذج محدد
   */
  async loadModel(
    modelId: string, 
    task: ModelTask,
    onProgress?: (progress: ModelLoadProgress) => void
  ): Promise<Pipeline> {
    // التحقق من وجود النموذج محملاً مسبقاً
    const existingModel = this.loadedModels.get(modelId);
    if (existingModel) {
      existingModel.lastUsed = Date.now();
      return existingModel.pipeline;
    }

    // التحقق من وجود عملية تحميل جارية
    const existingPromise = this.loadingPromises.get(modelId);
    if (existingPromise) {
      return existingPromise;
    }

    // تسجيل callback للتقدم
    if (onProgress) {
      this.progressCallbacks.set(modelId, onProgress);
    }

    // بدء تحميل النموذج
    const loadPromise = this.performModelLoad(modelId, task);
    this.loadingPromises.set(modelId, loadPromise);

    try {
      const pipeline = await loadPromise;
      this.loadingPromises.delete(modelId);
      this.progressCallbacks.delete(modelId);
      return pipeline;
    } catch (error) {
      this.loadingPromises.delete(modelId);
      this.progressCallbacks.delete(modelId);
      throw error;
    }
  }

  /**
   * تنفيذ تحميل النموذج الفعلي
   */
  private async performModelLoad(modelId: string, task: ModelTask): Promise<Pipeline> {
    const config = this.getModelConfig(modelId, task);
    if (!config) {
      throw new Error(`تكوين النموذج غير موجود: ${modelId}`);
    }

    this.reportProgress(modelId, 0, 'downloading');

    try {
      // التحقق من الذاكرة المتاحة
      await this.ensureMemoryAvailable(this.estimateModelMemory(config));

      // تحميل النموذج
      const pipelineInstance = await pipeline(
        this.getTaskType(task),
        config.id,
        {
          progress_callback: (progress: any) => {
            const percentage = Math.round((progress.loaded / progress.total) * 100);
            this.reportProgress(modelId, percentage, 'loading');
          }
        }
      );

      // حفظ النموذج المحمل
      const loadedModel: LoadedModel = {
        pipeline: pipelineInstance,
        config,
        loadedAt: Date.now(),
        lastUsed: Date.now(),
        memoryUsage: this.estimateModelMemory(config)
      };

      this.loadedModels.set(modelId, loadedModel);
      this.memoryUsage += loadedModel.memoryUsage;

      this.reportProgress(modelId, 100, 'ready');
      console.log(`✅ تم تحميل النموذج بنجاح: ${config.name} (${config.size})`);

      return pipelineInstance;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      this.reportProgress(modelId, 0, 'error', errorMessage);
      console.error(`❌ فشل في تحميل النموذج ${modelId}:`, error);
      throw new Error(`فشل في تحميل النموذج: ${errorMessage}`);
    }
  }

  /**
   * الحصول على أفضل نموذج للمهمة واللغة
   */
  getBestModel(task: ModelTask, language: SupportedLanguage): ModelConfig | null {
    const models = TRANSFORMERS_CONFIG.models[task];
    const modelList = Object.values(models);

    // ترتيب النماذج حسب الأولوية واللغة
    const sortedModels = modelList
      .filter(model => model.language === language || model.language === 'multilingual')
      .sort((a, b) => a.priority - b.priority);

    return sortedModels[0] || null;
  }

  /**
   * تحرير الذاكرة بإزالة النماذج غير المستخدمة
   */
  async freeMemory(forceCleanup: boolean = false): Promise<void> {
    const maxMemory = TRANSFORMERS_CONFIG.performance.maxMemoryUsage * 1024 * 1024; // تحويل إلى بايت
    const currentTime = Date.now();
    const maxIdleTime = 10 * 60 * 1000; // 10 دقائق

    for (const [modelId, model] of this.loadedModels.entries()) {
      const isIdle = currentTime - model.lastUsed > maxIdleTime;
      const shouldRemove = forceCleanup || (this.memoryUsage > maxMemory && isIdle);

      if (shouldRemove) {
        await this.unloadModel(modelId);
      }
    }
  }

  /**
   * إلغاء تحميل نموذج محدد
   */
  async unloadModel(modelId: string): Promise<void> {
    const model = this.loadedModels.get(modelId);
    if (model) {
      // تحرير موارد النموذج
      if (model.pipeline && typeof model.pipeline.dispose === 'function') {
        await model.pipeline.dispose();
      }

      this.memoryUsage -= model.memoryUsage;
      this.loadedModels.delete(modelId);
      
      console.log(`🗑️ تم إلغاء تحميل النموذج: ${model.config.name}`);
    }
  }

  /**
   * الحصول على إحصائيات الذاكرة
   */
  getMemoryStats(): {
    totalUsage: number;
    maxUsage: number;
    loadedModels: number;
    availableMemory: number;
  } {
    const maxMemory = TRANSFORMERS_CONFIG.performance.maxMemoryUsage * 1024 * 1024;
    return {
      totalUsage: this.memoryUsage,
      maxUsage: maxMemory,
      loadedModels: this.loadedModels.size,
      availableMemory: maxMemory - this.memoryUsage
    };
  }

  // الطرق المساعدة الخاصة

  private getModelConfig(modelId: string, task: ModelTask): ModelConfig | null {
    const models = TRANSFORMERS_CONFIG.models[task];
    return Object.values(models).find(model => model.id === modelId) || null;
  }

  private getTaskType(task: ModelTask): string {
    const taskMap = {
      'ocr': 'image-to-text',
      'ner': 'token-classification',
      'classification': 'text-classification'
    };
    return taskMap[task];
  }

  private estimateModelMemory(config: ModelConfig): number {
    // تقدير تقريبي للذاكرة بناءً على حجم النموذج
    const sizeStr = config.size.replace(/[^\d]/g, '');
    const sizeNum = parseInt(sizeStr) || 50;
    return sizeNum * 1024 * 1024; // تحويل إلى بايت
  }

  private async ensureMemoryAvailable(requiredMemory: number): Promise<void> {
    const maxMemory = TRANSFORMERS_CONFIG.performance.maxMemoryUsage * 1024 * 1024;
    
    if (this.memoryUsage + requiredMemory > maxMemory) {
      await this.freeMemory();
      
      if (this.memoryUsage + requiredMemory > maxMemory) {
        throw new Error('الذاكرة المتاحة غير كافية لتحميل النموذج');
      }
    }
  }

  private reportProgress(
    modelId: string, 
    progress: number, 
    status: ModelLoadProgress['status'], 
    error?: string
  ): void {
    const callback = this.progressCallbacks.get(modelId);
    if (callback) {
      callback({ modelId, progress, status, error });
    }
  }

  private setupMemoryMonitoring(): void {
    // مراقبة الذاكرة كل دقيقة
    setInterval(() => {
      this.freeMemory();
    }, 60000);
  }
}

// تصدير المثيل الوحيد
export const modelManager = LocalModelManager.getInstance();
