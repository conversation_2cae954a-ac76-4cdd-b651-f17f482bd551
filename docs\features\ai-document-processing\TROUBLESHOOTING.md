# 🔧 دليل استكشاف الأخطاء وإصلاحها - النظام المحلي

## 📋 نظرة عامة
هذا الدليل يساعد في حل المشاكل الشائعة التي قد تواجهها أثناء استخدام **النظام المحلي** لمعالجة المستندات الذكي باستخدام **Transformers.js**.

## 🚨 المشاكل الشائعة والحلول

### 1. **مشاكل تحميل النماذج**

#### ❌ **المشكلة: فشل في تحميل النموذج**
```
Error: Failed to load model 'Xenova/trocr-base-printed'
```

#### ✅ **الحلول:**

**أ) التحقق من الاتصال بالإنترنت:**
```typescript
// التحقق من الاتصال
if (!navigator.onLine) {
  console.error('❌ لا يوجد اتصال بالإنترنت');
  // استخدام نماذج محفوظة محلياً أو إظهار رسالة للمستخدم
}
```

**ب) زيادة المهلة الزمنية:**
```typescript
import { advancedModelManager } from '@/services/advancedModelManager';

try {
  await advancedModelManager.loadModel('Xenova/trocr-base-printed', 'ocr', {
    timeout: 180000 // 3 دقائق بدلاً من دقيقتين
  });
} catch (error) {
  console.error('فشل تحميل النموذج:', error);
}
```

**ج) تنظيف الكاش وإعادة المحاولة:**
```typescript
// تنظيف كاش النماذج
await advancedModelManager.clearModelCache();

// إعادة المحاولة
await advancedModelManager.loadModel(modelId, task);
```

---

### 2. **مشاكل الذاكرة**

#### ❌ **المشكلة: نفاد الذاكرة**
```
Error: الذاكرة المتاحة غير كافية لتحميل النموذج
```

#### ✅ **الحلول:**

**أ) تحرير الذاكرة فوراً:**
```typescript
import { modelOptimizer } from '@/services/modelOptimizer';

// تحرير الذاكرة بقوة
await modelOptimizer.runManualOptimization([
  'memory_cleanup',
  'garbage_collection',
  'cache_optimization'
]);

// التحقق من الذاكرة المحررة
const stats = advancedModelManager.getMemoryStats();
console.log(`الذاكرة المتاحة: ${stats.availableMemory}MB`);
```

**ب) إلغاء تحميل النماذج غير المستخدمة:**
```typescript
// الحصول على النماذج المحملة
const loadedModels = advancedModelManager.getLoadedModels();

// إلغاء تحميل النماذج القديمة
for (const model of loadedModels) {
  if (model.lastUsed && Date.now() - model.lastUsed > 300000) { // 5 دقائق
    await advancedModelManager.unloadModel(model.id);
    console.log(`تم إلغاء تحميل النموذج: ${model.name}`);
  }
}
```

**ج) تفعيل الوضع العدواني للتحسين:**
```typescript
modelOptimizer.updateConfig({
  aggressiveMode: true,
  memoryThreshold: 70, // تحسين عند 70% بدلاً من 80%
  enableAutoOptimization: true
});
```

---

### 3. **مشاكل الأداء**

#### ❌ **المشكلة: بطء في المعالجة**
```
معالجة المستند تستغرق أكثر من 30 ثانية
```

#### ✅ **الحلول:**

**أ) تحميل النماذج مسبقاً:**
```typescript
// تحميل النماذج الأساسية قبل المعالجة
await advancedModelManager.preloadEssentialModels((progress, current) => {
  console.log(`تحميل مسبق: ${progress}% - ${current}`);
});
```

**ب) تحسين إعدادات النماذج:**
```typescript
// استخدام نماذج أصغر وأسرع
const FAST_MODELS = {
  ocr: 'Xenova/trocr-small-printed',      // نموذج أصغر
  ner: 'Xenova/distilbert-base-cased',   // نموذج مضغوط
  classification: 'Xenova/distilbert-base-uncased-finetuned-sst-2-english'
};

// تحديث تكوين النماذج
await advancedModelManager.updateModelConfig(FAST_MODELS);
```

**ج) تفعيل المعالجة المتوازية:**
```typescript
// تفعيل Web Workers للمعالجة المتوازية
const config = {
  useWebWorkers: true,
  maxWorkers: navigator.hardwareConcurrency || 4
};

await advancedModelManager.updateConfig(config);
```

---

### 4. **مشاكل جودة النتائج**

#### ❌ **المشكلة: دقة منخفضة في استخراج النص**
```
OCR confidence: 0.3 (منخفض جداً)
```

#### ✅ **الحلول:**

**أ) تحسين جودة الصورة:**
```typescript
// معالجة الصورة قبل OCR
const preprocessImage = async (imageUrl: string): Promise<string> => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d')!;
  const img = new Image();
  
  return new Promise((resolve) => {
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      
      // تحسين التباين والسطوع
      ctx.filter = 'contrast(1.2) brightness(1.1)';
      ctx.drawImage(img, 0, 0);
      
      resolve(canvas.toDataURL());
    };
    img.src = imageUrl;
  });
};

// استخدام الصورة المحسنة
const enhancedImageUrl = await preprocessImage(originalImageUrl);
const ocrResult = await ocrService.extractText(enhancedImageUrl, mimeType);
```

**ب) استخدام نماذج متخصصة:**
```typescript
// اختيار النموذج المناسب لنوع النص
const selectOCRModel = (textType: 'printed' | 'handwritten') => {
  return textType === 'printed' 
    ? 'Xenova/trocr-base-printed'
    : 'Xenova/trocr-base-handwritten';
};

const modelId = selectOCRModel('printed');
await ocrService.setModel(modelId);
```

**ج) دمج نتائج متعددة:**
```typescript
// استخدام عدة نماذج ودمج النتائج
const multiModelOCR = async (imageUrl: string) => {
  const results = await Promise.all([
    ocrService.extractText(imageUrl, 'Xenova/trocr-base-printed'),
    ocrService.extractText(imageUrl, 'tesseract.js'),
  ]);
  
  // اختيار النتيجة الأفضل
  const bestResult = results.reduce((best, current) => 
    current.confidence > best.confidence ? current : best
  );
  
  return bestResult;
};
```

---

### 5. **مشاكل التكامل**

#### ❌ **المشكلة: فشل في حفظ النتائج**
```
Error: Failed to save processing results to database
```

#### ✅ **الحلول:**

**أ) إعادة المحاولة مع تأخير:**
```typescript
const saveWithRetry = async (data: any, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await fetch('/api/ai-document-processing/save', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      
      console.log('✅ تم حفظ النتائج بنجاح');
      return;
      
    } catch (error) {
      console.warn(`⚠️ فشل الحفظ - المحاولة ${attempt}/${maxRetries}`);
      
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      } else {
        throw error;
      }
    }
  }
};
```

**ب) حفظ محلي احتياطي:**
```typescript
// حفظ النتائج محلياً كنسخة احتياطية
const saveLocally = (data: any) => {
  const backup = {
    timestamp: Date.now(),
    data: data
  };
  
  localStorage.setItem(`backup_${data.processingId}`, JSON.stringify(backup));
  console.log('💾 تم حفظ نسخة احتياطية محلية');
};

// استرداد النسخ الاحتياطية
const restoreBackups = () => {
  const backups = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key?.startsWith('backup_')) {
      const backup = JSON.parse(localStorage.getItem(key)!);
      backups.push(backup);
    }
  }
  return backups;
};
```

---

### 6. **مشاكل المتصفح**

#### ❌ **المشكلة: عدم دعم المتصفح للميزات المطلوبة**
```
Error: WebAssembly is not supported in this browser
```

#### ✅ **الحلول:**

**أ) التحقق من دعم المتصفح:**
```typescript
const checkBrowserSupport = () => {
  const requirements = {
    webAssembly: typeof WebAssembly !== 'undefined',
    webWorkers: typeof Worker !== 'undefined',
    indexedDB: 'indexedDB' in window,
    fetch: typeof fetch !== 'undefined'
  };
  
  const unsupported = Object.entries(requirements)
    .filter(([_, supported]) => !supported)
    .map(([feature, _]) => feature);
  
  if (unsupported.length > 0) {
    console.error('❌ ميزات غير مدعومة:', unsupported);
    return false;
  }
  
  console.log('✅ المتصفح يدعم جميع الميزات المطلوبة');
  return true;
};

// التحقق قبل بدء النظام
if (!checkBrowserSupport()) {
  // عرض رسالة للمستخدم لتحديث المتصفح
  showBrowserUpdateMessage();
}
```

**ب) استخدام Polyfills:**
```typescript
// تحميل polyfills للمتصفحات القديمة
const loadPolyfills = async () => {
  if (!window.fetch) {
    await import('whatwg-fetch');
  }
  
  if (!window.Promise) {
    await import('es6-promise/auto');
  }
};

await loadPolyfills();
```

---

## 🛠️ أدوات التشخيص

### 📊 **فحص صحة النظام**

```typescript
import { systemHealthMonitor } from '@/services/systemHealthMonitor';

// فحص شامل للنظام
const runDiagnostics = async () => {
  console.log('🔍 بدء فحص النظام...');
  
  const health = await systemHealthMonitor.performHealthCheck();
  
  console.log('📊 نتائج الفحص:');
  console.log(`- الحالة العامة: ${health.overall}`);
  console.log(`- النقاط: ${health.score}/100`);
  console.log(`- المكونات:`);
  
  for (const [component, status] of Object.entries(health.components)) {
    console.log(`  - ${component}: ${status.status} (${status.score}/100)`);
    
    if (status.issues.length > 0) {
      console.log(`    مشاكل: ${status.issues.join(', ')}`);
    }
  }
  
  if (health.recommendations.length > 0) {
    console.log('💡 التوصيات:');
    health.recommendations.forEach(rec => console.log(`  - ${rec}`));
  }
  
  return health;
};

// تشغيل الفحص
const diagnostics = await runDiagnostics();
```

### 🧠 **فحص الذاكرة**

```typescript
const memoryDiagnostics = () => {
  const stats = advancedModelManager.getDetailedMemoryStats();
  
  console.log('🧠 تشخيص الذاكرة:');
  console.log(`- الاستخدام الإجمالي: ${stats.totalUsage}MB`);
  console.log(`- الحد الأقصى: ${stats.maxUsage}MB`);
  console.log(`- المتاح: ${stats.availableMemory}MB`);
  console.log(`- النماذج المحملة: ${stats.loadedModels}`);
  
  console.log('📋 تفصيل النماذج:');
  stats.modelBreakdown.forEach(model => {
    console.log(`  - ${model.name}: ${model.memoryUsage}MB (${model.status})`);
  });
  
  if (stats.recommendations.length > 0) {
    console.log('💡 توصيات الذاكرة:');
    stats.recommendations.forEach(rec => console.log(`  - ${rec}`));
  }
  
  return stats;
};
```

### ⚡ **فحص الأداء**

```typescript
const performanceDiagnostics = () => {
  const report = modelOptimizer.getOptimizationReport();
  
  console.log('⚡ تشخيص الأداء:');
  console.log(`- إجمالي التحسينات: ${report.totalOptimizations}`);
  console.log(`- معدل النجاح: ${report.successRate}%`);
  console.log(`- الذاكرة المحررة: ${report.totalMemoryFreed}MB`);
  console.log(`- متوسط تحسن الأداء: ${report.averagePerformanceGain}%`);
  
  if (report.recentOptimizations.length > 0) {
    console.log('📈 التحسينات الأخيرة:');
    report.recentOptimizations.slice(-5).forEach(opt => {
      console.log(`  - ${opt.strategy}: ${opt.memoryFreed}MB محررة`);
    });
  }
  
  return report;
};
```

## 🆘 الحصول على المساعدة

### 📞 **خطوات طلب المساعدة**

1. **جمع معلومات النظام:**
```typescript
const collectSystemInfo = () => {
  return {
    browser: navigator.userAgent,
    timestamp: new Date().toISOString(),
    health: systemHealthMonitor.getCurrentHealth(),
    memory: advancedModelManager.getDetailedMemoryStats(),
    performance: modelOptimizer.getOptimizationReport(),
    errors: getRecentErrors() // تنفيذ مخصص
  };
};

const systemInfo = collectSystemInfo();
console.log('📋 معلومات النظام:', systemInfo);
```

2. **إنشاء تقرير مفصل:**
```typescript
const generateTroubleshootingReport = () => {
  const report = {
    timestamp: Date.now(),
    systemInfo: collectSystemInfo(),
    diagnostics: {
      health: systemHealthMonitor.getCurrentHealth(),
      memory: memoryDiagnostics(),
      performance: performanceDiagnostics()
    },
    recentLogs: getRecentLogs(), // تنفيذ مخصص
    userActions: getUserActionHistory() // تنفيذ مخصص
  };
  
  // حفظ التقرير
  const blob = new Blob([JSON.stringify(report, null, 2)], {
    type: 'application/json'
  });
  
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `troubleshooting-report-${Date.now()}.json`;
  a.click();
  
  return report;
};
```

### 📧 **معلومات الاتصال**

- **الدعم التقني:** <EMAIL>
- **التوثيق:** [docs/features/ai-document-processing/](./README.md)
- **المشاكل المعروفة:** [KNOWN_ISSUES.md](./KNOWN_ISSUES.md)

---

## 🎯 نصائح الوقاية

### ✅ **أفضل الممارسات**

1. **مراقبة دورية للنظام**
2. **تحديث النماذج بانتظام**
3. **تنظيف الذاكرة دورياً**
4. **اختبار الأداء بانتظام**
5. **نسخ احتياطية للإعدادات**

### ⚠️ **تجنب**

1. **تحميل نماذج متعددة بدون ضرورة**
2. **تجاهل تنبيهات الذاكرة**
3. **عدم تحديث المتصفح**
4. **تشغيل عمليات متوازية مكثفة**

**النظام المحلي مصمم ليكون موثوقاً ومستقراً - اتبع هذا الدليل لحل أي مشاكل قد تواجهها!** 🚀
