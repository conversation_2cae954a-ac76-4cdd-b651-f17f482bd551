// cypress/e2e/local-document-processing.cy.ts - اختبارات المعالجة المحلية الكاملة

describe('📄 معالجة المستندات المحلية - Transformers.js', () => {
  let processingEvents: any[] = [];
  let networkRequests: any[] = [];

  beforeEach(() => {
    processingEvents = [];
    networkRequests = [];

    // مراقبة أحداث المعالجة
    cy.window().then((win) => {
      win.addEventListener('processing-stage-change', (event: any) => {
        processingEvents.push({
          stage: event.detail.stage,
          progress: event.detail.progress,
          timestamp: Date.now()
        });
      });

      win.addEventListener('processing-complete', (event: any) => {
        processingEvents.push({
          type: 'complete',
          result: event.detail.result,
          timestamp: Date.now()
        });
      });
    });

    // مراقبة طلبات الشبكة
    cy.intercept('**/*', (req) => {
      networkRequests.push({
        url: req.url,
        method: req.method,
        timestamp: Date.now()
      });

      // حظر طلبات API الخارجية للمعالجة
      const blockedDomains = [
        'api.huggingface.co',
        'api.openai.com',
        'generativelanguage.googleapis.com'
      ];

      if (blockedDomains.some(domain => req.url.includes(domain)) && req.method === 'POST') {
        req.reply({ statusCode: 403, body: 'محظور: استخدم المعالجة المحلية' });
      }
    });

    cy.login('merchant');
    cy.visit('/ai-document-processing');
  });

  describe('🔍 المرحلة الأولى: OCR المحلي', () => {
    it('يجب أن يستخرج النص من الصور محلياً', () => {
      // تحضير ملف اختبار
      cy.fixture('test-document.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'test-document.jpg', {
          type: 'image/jpeg'
        });

        // رفع الملف
        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        
        // بدء المعالجة
        cy.get('[data-testid="process-document-btn"]').click();
        
        // التحقق من بدء مرحلة OCR
        cy.get('[data-testid="ocr-stage"]').should('be.visible');
        cy.contains('استخراج النص من المستند').should('be.visible');
        
        // التحقق من تقدم المعالجة
        cy.get('[data-testid="ocr-progress"]').should('be.visible');
        cy.contains('تحميل نموذج OCR المحلي').should('be.visible');
        
        // انتظار اكتمال OCR
        cy.get('[data-testid="ocr-results"]', { timeout: 30000 }).should('be.visible');
        
        // التحقق من وجود النص المستخرج
        cy.get('[data-testid="extracted-text"]').should('not.be.empty');
        
        // التحقق من معلومات النموذج المستخدم
        cy.get('[data-testid="ocr-model-info"]').should('contain', 'TrOCR');
        cy.get('[data-testid="ocr-confidence"]').should('be.visible');
        
        // التحقق من عدم وجود طلبات API خارجية
        cy.then(() => {
          const externalOCRCalls = networkRequests.filter(req => 
            req.url.includes('api.huggingface.co') && req.method === 'POST'
          );
          expect(externalOCRCalls).to.have.length(0);
        });
      });
    });

    it('يجب أن يتعامل مع أنواع مختلفة من النصوص', () => {
      // اختبار النصوص المطبوعة
      cy.fixture('printed-text.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'printed-text.jpg', {
          type: 'image/jpeg'
        });

        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="process-document-btn"]').click();
        
        cy.get('[data-testid="ocr-results"]', { timeout: 30000 }).should('be.visible');
        
        // التحقق من نوع النموذج المستخدم
        cy.get('[data-testid="ocr-model-info"]').should('contain', 'printed');
      });
    });

    it('يجب أن يكشف اللغة تلقائياً', () => {
      cy.fixture('arabic-document.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'arabic-document.jpg', {
          type: 'image/jpeg'
        });

        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="process-document-btn"]').click();
        
        cy.get('[data-testid="ocr-results"]', { timeout: 30000 }).should('be.visible');
        
        // التحقق من كشف اللغة العربية
        cy.get('[data-testid="detected-language"]').should('contain', 'ar');
      });
    });
  });

  describe('🔎 المرحلة الثانية: NER المحلي', () => {
    it('يجب أن يستخرج الكيانات والبيانات محلياً', () => {
      // محاكاة نص مستخرج من OCR
      const extractedText = 'رقم الهوية: 1234567890 الاسم: أحمد محمد تاريخ الميلاد: 15/03/1990';
      
      // رفع مستند وانتظار OCR
      cy.fixture('test-id-card.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'test-id-card.jpg', {
          type: 'image/jpeg'
        });

        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="process-document-btn"]').click();
        
        // انتظار اكتمال OCR والانتقال لـ NER
        cy.get('[data-testid="ner-stage"]', { timeout: 30000 }).should('be.visible');
        cy.contains('استخلاص البيانات والكيانات').should('be.visible');
        
        // التحقق من تقدم NER
        cy.get('[data-testid="ner-progress"]').should('be.visible');
        cy.contains('تحميل نموذج استخلاص البيانات المحلي').should('be.visible');
        
        // انتظار اكتمال NER
        cy.get('[data-testid="ner-results"]', { timeout: 30000 }).should('be.visible');
        
        // التحقق من استخراج الكيانات
        cy.get('[data-testid="extracted-entities"]').should('be.visible');
        cy.get('[data-testid="entity-person"]').should('exist');
        cy.get('[data-testid="entity-number"]').should('exist');
        cy.get('[data-testid="entity-date"]').should('exist');
        
        // التحقق من البيانات المهيكلة
        cy.get('[data-testid="structured-data"]').should('be.visible');
        cy.get('[data-testid="field-nationalId"]').should('be.visible');
        cy.get('[data-testid="field-name"]').should('be.visible');
        cy.get('[data-testid="field-birthDate"]').should('be.visible');
        
        // التحقق من النموذج المستخدم
        cy.get('[data-testid="ner-model-info"]').should('contain', 'BERT');
      });
    });

    it('يجب أن يتعامل مع أنواع مستندات مختلفة', () => {
      // اختبار السجل التجاري
      cy.fixture('commercial-registration.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'commercial-registration.jpg', {
          type: 'image/jpeg'
        });

        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="process-document-btn"]').click();
        
        cy.get('[data-testid="ner-results"]', { timeout: 45000 }).should('be.visible');
        
        // التحقق من استخراج بيانات السجل التجاري
        cy.get('[data-testid="field-registrationNumber"]').should('be.visible');
        cy.get('[data-testid="field-businessName"]').should('be.visible');
        cy.get('[data-testid="field-ownerName"]').should('be.visible');
      });
    });

    it('يجب أن يدمج نتائج النماذج والأنماط', () => {
      cy.fixture('test-document.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'test-document.jpg', {
          type: 'image/jpeg'
        });

        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="process-document-btn"]').click();
        
        cy.get('[data-testid="ner-results"]', { timeout: 45000 }).should('be.visible');
        
        // التحقق من مصادر الاستخراج
        cy.get('[data-testid="extraction-sources"]').should('be.visible');
        cy.get('[data-testid="source-model"]').should('exist');
        cy.get('[data-testid="source-pattern"]').should('exist');
        
        // التحقق من دمج النتائج
        cy.get('[data-testid="merged-entities"]').should('be.visible');
      });
    });
  });

  describe('📊 المرحلة الثالثة: التصنيف المحلي', () => {
    it('يجب أن يصنف المستند ويتخذ قراراً محلياً', () => {
      cy.fixture('test-id-card.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'test-id-card.jpg', {
          type: 'image/jpeg'
        });

        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="process-document-btn"]').click();
        
        // انتظار الوصول لمرحلة التصنيف
        cy.get('[data-testid="classification-stage"]', { timeout: 60000 }).should('be.visible');
        cy.contains('تصنيف المستند واتخاذ القرار').should('be.visible');
        
        // التحقق من تقدم التصنيف
        cy.get('[data-testid="classification-progress"]').should('be.visible');
        
        // انتظار اكتمال التصنيف
        cy.get('[data-testid="classification-results"]', { timeout: 30000 }).should('be.visible');
        
        // التحقق من نوع المستند
        cy.get('[data-testid="document-type"]').should('contain', 'identity');
        
        // التحقق من القرار
        cy.get('[data-testid="decision-result"]').should('be.visible');
        cy.get('[data-testid="decision-status"]').should('be.oneOf', ['approved', 'rejected', 'requires_review']);
        
        // التحقق من مستوى الثقة
        cy.get('[data-testid="confidence-score"]').should('be.visible');
        
        // التحقق من التبرير
        cy.get('[data-testid="decision-reasoning"]').should('be.visible');
        
        // التحقق من الإجراءات المطلوبة (إن وجدت)
        cy.get('[data-testid="required-actions"]').should('be.visible');
      });
    });

    it('يجب أن يطبق قواعد مختلفة للتجار والمندوبين', () => {
      // اختبار كتاجر
      cy.fixture('test-document.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'test-document.jpg', {
          type: 'image/jpeg'
        });

        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="process-document-btn"]').click();
        
        cy.get('[data-testid="classification-results"]', { timeout: 60000 }).should('be.visible');
        
        // التحقق من تطبيق قواعد التاجر
        cy.get('[data-testid="user-type"]').should('contain', 'merchant');
        cy.get('[data-testid="applied-rules"]').should('contain', 'merchant');
      });
    });

    it('يجب أن يحلل جودة البيانات', () => {
      cy.fixture('test-document.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'test-document.jpg', {
          type: 'image/jpeg'
        });

        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="process-document-btn"]').click();
        
        cy.get('[data-testid="classification-results"]', { timeout: 60000 }).should('be.visible');
        
        // التحقق من تحليل الجودة
        cy.get('[data-testid="quality-analysis"]').should('be.visible');
        cy.get('[data-testid="data-completeness"]').should('be.visible');
        cy.get('[data-testid="confidence-score"]').should('be.visible');
        cy.get('[data-testid="document-quality"]').should('be.visible');
        cy.get('[data-testid="consistency-check"]').should('be.visible');
      });
    });
  });

  describe('🎯 المعالجة الكاملة', () => {
    it('يجب أن يكمل المعالجة الثلاثية بنجاح', () => {
      cy.fixture('complete-test-document.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'complete-test-document.jpg', {
          type: 'image/jpeg'
        });

        const startTime = Date.now();

        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="process-document-btn"]').click();
        
        // مراقبة تقدم جميع المراحل
        cy.get('[data-testid="ocr-stage"]').should('be.visible');
        cy.get('[data-testid="ner-stage"]', { timeout: 30000 }).should('be.visible');
        cy.get('[data-testid="classification-stage"]', { timeout: 60000 }).should('be.visible');
        
        // انتظار اكتمال المعالجة
        cy.get('[data-testid="processing-complete"]', { timeout: 90000 }).should('be.visible');
        
        // التحقق من النتائج النهائية
        cy.get('[data-testid="final-results"]').should('be.visible');
        cy.get('[data-testid="overall-confidence"]').should('be.visible');
        cy.get('[data-testid="processing-time"]').should('be.visible');
        
        // التحقق من وقت المعالجة
        cy.get('[data-testid="processing-time"]').then(($el) => {
          const processingTime = parseInt($el.text());
          expect(processingTime).to.be.lessThan(30000); // أقل من 30 ثانية
        });
        
        // التحقق من جميع النتائج
        cy.get('[data-testid="ocr-final-results"]').should('be.visible');
        cy.get('[data-testid="ner-final-results"]').should('be.visible');
        cy.get('[data-testid="classification-final-results"]').should('be.visible');
        
        // التحقق من التحذيرات (إن وجدت)
        cy.get('[data-testid="warnings"]').should('be.visible');
        
        // التحقق من عدم وجود طلبات API خارجية
        cy.then(() => {
          const externalAPICalls = networkRequests.filter(req => 
            (req.url.includes('api.huggingface.co') ||
             req.url.includes('api.openai.com') ||
             req.url.includes('generativelanguage.googleapis.com')) &&
            req.method === 'POST'
          );
          expect(externalAPICalls).to.have.length(0);
        });
      });
    });

    it('يجب أن يحفظ النتائج في قاعدة البيانات', () => {
      cy.fixture('test-document.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'test-document.jpg', {
          type: 'image/jpeg'
        });

        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="process-document-btn"]').click();
        
        cy.get('[data-testid="processing-complete"]', { timeout: 90000 }).should('be.visible');
        
        // التحقق من حفظ النتائج
        cy.get('[data-testid="processing-id"]').should('be.visible');
        cy.get('[data-testid="save-status"]').should('contain', 'تم الحفظ');
        
        // التحقق من إمكانية الوصول للنتائج لاحقاً
        cy.get('[data-testid="view-results-btn"]').click();
        cy.get('[data-testid="saved-results"]').should('be.visible');
      });
    });

    it('يجب أن يتعامل مع الأخطاء بشكل صحيح', () => {
      // رفع ملف تالف
      const corruptedFile = new File(['corrupted data'], 'corrupted.jpg', {
        type: 'image/jpeg'
      });

      cy.get('[data-testid="file-input"]').selectFile(corruptedFile, { force: true });
      cy.get('[data-testid="process-document-btn"]').click();
      
      // التحقق من معالجة الخطأ
      cy.get('[data-testid="error-message"]', { timeout: 30000 }).should('be.visible');
      cy.contains('فشل في معالجة المستند').should('be.visible');
      
      // التحقق من عرض خيارات الاستعادة
      cy.get('[data-testid="retry-btn"]').should('be.visible');
      cy.get('[data-testid="fallback-options"]').should('be.visible');
    });
  });

  afterEach(() => {
    // تسجيل إحصائيات المعالجة
    cy.then(() => {
      console.log('📊 إحصائيات المعالجة:');
      console.log(`- أحداث المعالجة: ${processingEvents.length}`);
      console.log(`- طلبات الشبكة: ${networkRequests.length}`);
      
      const stages = processingEvents.filter(e => e.stage);
      console.log(`- مراحل المعالجة: ${stages.map(s => s.stage).join(', ')}`);
      
      const externalCalls = networkRequests.filter(req => 
        req.url.includes('api.') && req.method === 'POST'
      );
      console.log(`- طلبات API خارجية: ${externalCalls.length}`);
    });
  });
});
