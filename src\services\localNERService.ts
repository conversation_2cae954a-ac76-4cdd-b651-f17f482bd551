// src/services/localNERService.ts - خدمة استخلاص الكيانات والمعلومات المحلية

import { modelManager } from './localModelManager';
import { NERResult, Entity } from './huggingFaceAIService';
import { 
  TRANSFORMERS_CONFIG, 
  SupportedLanguage,
  NER_MODELS 
} from '../config/transformersConfig';

// أنماط استخلاص البيانات المحسّنة
const EXTRACTION_PATTERNS = {
  // أنماط الهوية الشخصية
  identity: {
    ar: {
      nationalId: /(?:رقم\s*الهوية|الرقم\s*الوطني|رقم\s*السجل)\s*:?\s*(\d{10})/gi,
      name: /(?:الاسم|اسم\s*المواطن|الاسم\s*الكامل)\s*:?\s*([أ-ي\s]{2,50})/gi,
      birthDate: /(?:تاريخ\s*الميلاد|الميلاد)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/gi,
      issueDate: /(?:تاريخ\s*الإصدار|الإصدار)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/gi,
      expiryDate: /(?:تاريخ\s*الانتهاء|الانتهاء)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/gi
    },
    en: {
      nationalId: /(?:ID\s*Number|National\s*ID|Identity\s*Number)\s*:?\s*(\d{10})/gi,
      name: /(?:Name|Full\s*Name)\s*:?\s*([A-Za-z\s]{2,50})/gi,
      birthDate: /(?:Date\s*of\s*Birth|Birth\s*Date|DOB)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/gi,
      issueDate: /(?:Issue\s*Date|Issued\s*On)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/gi,
      expiryDate: /(?:Expiry\s*Date|Expires\s*On)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/gi
    }
  },
  
  // أنماط السجل التجاري
  business: {
    ar: {
      registrationNumber: /(?:رقم\s*السجل|السجل\s*التجاري|رقم\s*التسجيل)\s*:?\s*(\d{10,15})/gi,
      businessName: /(?:اسم\s*المنشأة|الاسم\s*التجاري|اسم\s*الشركة)\s*:?\s*([أ-ي\s]{2,100})/gi,
      ownerName: /(?:اسم\s*المالك|صاحب\s*المنشأة|المالك)\s*:?\s*([أ-ي\s]{2,50})/gi,
      issueDate: /(?:تاريخ\s*الإصدار|الإصدار)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/gi,
      expiryDate: /(?:تاريخ\s*الانتهاء|الانتهاء)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/gi
    },
    en: {
      registrationNumber: /(?:Registration\s*Number|Business\s*License|CR\s*Number)\s*:?\s*(\d{10,15})/gi,
      businessName: /(?:Business\s*Name|Company\s*Name|Trade\s*Name)\s*:?\s*([A-Za-z\s&]{2,100})/gi,
      ownerName: /(?:Owner\s*Name|Proprietor)\s*:?\s*([A-Za-z\s]{2,50})/gi,
      issueDate: /(?:Issue\s*Date|Issued\s*On)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/gi,
      expiryDate: /(?:Expiry\s*Date|Expires\s*On)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/gi
    }
  }
};

// تصنيفات الكيانات
const ENTITY_TYPES = {
  PERSON: 'شخص',
  ORGANIZATION: 'منظمة',
  LOCATION: 'موقع',
  DATE: 'تاريخ',
  NUMBER: 'رقم',
  DOCUMENT: 'مستند'
};

/**
 * خدمة استخلاص الكيانات والمعلومات المحلية
 */
export class LocalNERService {
  private static instance: LocalNERService;

  private constructor() {}

  static getInstance(): LocalNERService {
    if (!LocalNERService.instance) {
      LocalNERService.instance = new LocalNERService();
    }
    return LocalNERService.instance;
  }

  /**
   * استخلاص الكيانات والبيانات من النص
   */
  async extractEntitiesAndData(
    text: string, 
    language: SupportedLanguage,
    documentType?: string,
    onProgress?: (progress: { stage: string; percentage: number }) => void
  ): Promise<NERResult> {
    const startTime = Date.now();
    
    try {
      // المرحلة 1: تحضير النص
      onProgress?.({ stage: 'تحضير النص للتحليل', percentage: 10 });
      const preprocessedText = this.preprocessText(text);
      
      // المرحلة 2: اختيار النموذج المناسب
      const modelId = this.selectBestNERModel(language);
      
      // المرحلة 3: تحميل النموذج
      onProgress?.({ stage: 'تحميل نموذج استخلاص الكيانات', percentage: 20 });
      const pipeline = await modelManager.loadModel(
        modelId, 
        'ner',
        (progress) => {
          const adjustedPercentage = 20 + (progress.progress * 0.3); // 20-50%
          onProgress?.({ stage: `تحميل النموذج: ${progress.status}`, percentage: adjustedPercentage });
        }
      );
      
      // المرحلة 4: استخلاص الكيانات باستخدام النموذج
      onProgress?.({ stage: 'استخلاص الكيانات', percentage: 50 });
      const modelEntities = await this.extractWithModel(pipeline, preprocessedText);
      
      // المرحلة 5: استخلاص البيانات باستخدام الأنماط
      onProgress?.({ stage: 'استخلاص البيانات المهيكلة', percentage: 70 });
      const patternData = this.extractWithPatterns(text, language, documentType);
      
      // المرحلة 6: دمج وتحسين النتائج
      onProgress?.({ stage: 'دمج وتحسين النتائج', percentage: 85 });
      const mergedEntities = this.mergeEntities(modelEntities, patternData.entities);
      const refinedData = this.refineExtractedData(patternData.data, mergedEntities);
      
      // المرحلة 7: حساب الثقة
      onProgress?.({ stage: 'حساب مستوى الثقة', percentage: 95 });
      const confidence = this.calculateOverallConfidence(mergedEntities, refinedData);
      
      onProgress?.({ stage: 'اكتمل', percentage: 100 });
      
      const result: NERResult = {
        entities: mergedEntities,
        extractedData: refinedData,
        confidence,
        processingTime: Date.now() - startTime,
        modelUsed: modelId,
        metadata: {
          textLength: text.length,
          entitiesFound: mergedEntities.length,
          dataFieldsExtracted: Object.keys(refinedData).length,
          language,
          documentType
        }
      };

      console.log(`✅ NER مكتمل: ${mergedEntities.length} كيان، ${Object.keys(refinedData).length} حقل`);
      return result;

    } catch (error) {
      console.error('❌ خطأ في NER المحلي:', error);
      
      // استخدام الأنماط فقط كبديل
      return await this.fallbackToPatterns(text, language, documentType);
    }
  }

  /**
   * معالجة مسبقة للنص
   */
  private preprocessText(text: string): string {
    return text
      // توحيد المسافات
      .replace(/\s+/g, ' ')
      // إزالة الأحرف الخاصة غير المرغوبة
      .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FFa-zA-Z0-9\u0660-\u0669\s\-_.,;:()\[\]]/g, ' ')
      // تنظيف النص
      .trim();
  }

  /**
   * اختيار أفضل نموذج NER
   */
  private selectBestNERModel(language: SupportedLanguage): string {
    switch (language) {
      case 'ar':
        return NER_MODELS.arabert_mini.id;
      case 'en':
        return NER_MODELS.distilbert_en.id;
      default:
        return NER_MODELS.bert_multilingual.id;
    }
  }

  /**
   * استخلاص الكيانات باستخدام النموذج
   */
  private async extractWithModel(pipeline: any, text: string): Promise<Entity[]> {
    try {
      const result = await pipeline(text);
      
      if (!Array.isArray(result)) {
        return [];
      }
      
      return result.map((item: any, index: number) => ({
        id: `entity_${index}`,
        text: item.word || item.entity || '',
        label: this.mapEntityLabel(item.entity_group || item.label || 'MISC'),
        confidence: item.score || 0.5,
        start: item.start || 0,
        end: item.end || 0,
        source: 'model'
      }));
      
    } catch (error) {
      console.warn('⚠️ فشل في استخلاص الكيانات بالنموذج:', error);
      return [];
    }
  }

  /**
   * استخلاص البيانات باستخدام الأنماط
   */
  private extractWithPatterns(
    text: string, 
    language: SupportedLanguage, 
    documentType?: string
  ): { entities: Entity[]; data: Record<string, any> } {
    const entities: Entity[] = [];
    const data: Record<string, any> = {};
    
    // تحديد نوع المستند إذا لم يكن محدداً
    const detectedType = documentType || this.detectDocumentType(text, language);
    
    // الحصول على الأنماط المناسبة
    const patterns = this.getPatternsForDocument(detectedType, language);
    
    // استخلاص البيانات باستخدام الأنماط
    Object.entries(patterns).forEach(([fieldName, pattern]) => {
      const matches = text.match(pattern);
      if (matches && matches[1]) {
        const value = matches[1].trim();
        data[fieldName] = value;
        
        // إضافة كيان
        entities.push({
          id: `pattern_${fieldName}`,
          text: value,
          label: this.getEntityTypeForField(fieldName),
          confidence: 0.8,
          start: text.indexOf(matches[0]),
          end: text.indexOf(matches[0]) + matches[0].length,
          source: 'pattern'
        });
      }
    });
    
    return { entities, data };
  }

  /**
   * دمج الكيانات من مصادر متعددة
   */
  private mergeEntities(modelEntities: Entity[], patternEntities: Entity[]): Entity[] {
    const merged = [...patternEntities]; // الأنماط لها أولوية أعلى
    
    // إضافة كيانات النموذج التي لا تتداخل
    modelEntities.forEach(modelEntity => {
      const hasOverlap = patternEntities.some(patternEntity => 
        this.entitiesOverlap(modelEntity, patternEntity)
      );
      
      if (!hasOverlap) {
        merged.push(modelEntity);
      }
    });
    
    return merged.sort((a, b) => a.start - b.start);
  }

  /**
   * تحسين البيانات المستخرجة
   */
  private refineExtractedData(data: Record<string, any>, entities: Entity[]): Record<string, any> {
    const refined = { ...data };
    
    // تنظيف وتحسين البيانات
    Object.keys(refined).forEach(key => {
      if (typeof refined[key] === 'string') {
        refined[key] = refined[key].trim();
        
        // تحسين التواريخ
        if (key.includes('Date') || key.includes('تاريخ')) {
          refined[key] = this.normalizeDate(refined[key]);
        }
        
        // تحسين الأرقام
        if (key.includes('Number') || key.includes('رقم')) {
          refined[key] = this.normalizeNumber(refined[key]);
        }
      }
    });
    
    return refined;
  }

  /**
   * حساب الثقة الإجمالية
   */
  private calculateOverallConfidence(entities: Entity[], data: Record<string, any>): number {
    if (entities.length === 0) return 0;
    
    // متوسط ثقة الكيانات
    const entitiesConfidence = entities.reduce((sum, entity) => sum + entity.confidence, 0) / entities.length;
    
    // عامل عدد البيانات المستخرجة
    const dataFactor = Math.min(Object.keys(data).length / 5, 1); // حتى 5 حقول
    
    // عامل جودة البيانات
    const qualityFactor = this.assessDataQuality(data);
    
    return Math.min((entitiesConfidence * 0.5) + (dataFactor * 0.3) + (qualityFactor * 0.2), 1);
  }

  // الطرق المساعدة

  private mapEntityLabel(label: string): string {
    const labelMap: Record<string, string> = {
      'PER': ENTITY_TYPES.PERSON,
      'PERSON': ENTITY_TYPES.PERSON,
      'ORG': ENTITY_TYPES.ORGANIZATION,
      'ORGANIZATION': ENTITY_TYPES.ORGANIZATION,
      'LOC': ENTITY_TYPES.LOCATION,
      'LOCATION': ENTITY_TYPES.LOCATION,
      'DATE': ENTITY_TYPES.DATE,
      'NUM': ENTITY_TYPES.NUMBER,
      'MISC': ENTITY_TYPES.DOCUMENT
    };
    
    return labelMap[label.toUpperCase()] || ENTITY_TYPES.DOCUMENT;
  }

  private detectDocumentType(text: string, language: SupportedLanguage): string {
    const keywords = {
      identity: language === 'ar' ? ['هوية', 'بطاقة', 'جواز'] : ['id', 'identity', 'passport'],
      business: language === 'ar' ? ['سجل', 'تجاري', 'رخصة'] : ['business', 'license', 'registration']
    };
    
    const lowerText = text.toLowerCase();
    
    if (keywords.identity.some(keyword => lowerText.includes(keyword))) {
      return 'identity';
    } else if (keywords.business.some(keyword => lowerText.includes(keyword))) {
      return 'business';
    }
    
    return 'general';
  }

  private getPatternsForDocument(documentType: string, language: SupportedLanguage): Record<string, RegExp> {
    const langKey = language === 'mixed' ? 'ar' : language;
    
    if (documentType === 'identity' && EXTRACTION_PATTERNS.identity[langKey as keyof typeof EXTRACTION_PATTERNS.identity]) {
      return EXTRACTION_PATTERNS.identity[langKey as keyof typeof EXTRACTION_PATTERNS.identity];
    } else if (documentType === 'business' && EXTRACTION_PATTERNS.business[langKey as keyof typeof EXTRACTION_PATTERNS.business]) {
      return EXTRACTION_PATTERNS.business[langKey as keyof typeof EXTRACTION_PATTERNS.business];
    }
    
    return {};
  }

  private getEntityTypeForField(fieldName: string): string {
    if (fieldName.includes('name') || fieldName.includes('اسم')) {
      return ENTITY_TYPES.PERSON;
    } else if (fieldName.includes('date') || fieldName.includes('تاريخ')) {
      return ENTITY_TYPES.DATE;
    } else if (fieldName.includes('number') || fieldName.includes('رقم')) {
      return ENTITY_TYPES.NUMBER;
    }
    return ENTITY_TYPES.DOCUMENT;
  }

  private entitiesOverlap(entity1: Entity, entity2: Entity): boolean {
    return !(entity1.end <= entity2.start || entity2.end <= entity1.start);
  }

  private normalizeDate(dateStr: string): string {
    // تحويل التواريخ إلى تنسيق موحد
    return dateStr.replace(/[\/\-]/g, '/');
  }

  private normalizeNumber(numberStr: string): string {
    // تحويل الأرقام العربية إلى إنجليزية
    return numberStr.replace(/[\u0660-\u0669]/g, (match) => 
      String.fromCharCode(match.charCodeAt(0) - 0x0660 + 0x0030)
    );
  }

  private assessDataQuality(data: Record<string, any>): number {
    let quality = 0;
    const fields = Object.keys(data);
    
    // عامل عدد الحقول
    quality += Math.min(fields.length / 5, 0.5);
    
    // عامل جودة البيانات
    fields.forEach(field => {
      const value = data[field];
      if (value && typeof value === 'string' && value.length > 2) {
        quality += 0.1;
      }
    });
    
    return Math.min(quality, 1);
  }

  /**
   * استخدام الأنماط فقط كبديل
   */
  private async fallbackToPatterns(
    text: string, 
    language: SupportedLanguage, 
    documentType?: string
  ): Promise<NERResult> {
    console.log('🔄 استخدام الأنماط فقط كبديل...');
    
    const result = this.extractWithPatterns(text, language, documentType);
    
    return {
      entities: result.entities,
      extractedData: result.data,
      confidence: 0.6, // ثقة متوسطة للأنماط فقط
      processingTime: 0,
      modelUsed: 'patterns-only',
      metadata: {
        fallback: true,
        textLength: text.length,
        entitiesFound: result.entities.length,
        dataFieldsExtracted: Object.keys(result.data).length,
        language,
        documentType
      }
    };
  }
}
