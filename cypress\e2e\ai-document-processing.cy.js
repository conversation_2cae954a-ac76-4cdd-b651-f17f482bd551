// cypress/e2e/ai-document-processing.cy.js - اختبارات شاملة لنظام معالجة المستندات الذكي المحلي

describe('نظام معالجة المستندات الذكي المحلي - Transformers.js', () => {
  let networkRequests = [];

  beforeEach(() => {
    networkRequests = [];

    // مراقبة طلبات الشبكة للتأكد من المعالجة المحلية
    cy.intercept('**/*', (req) => {
      networkRequests.push({
        url: req.url,
        method: req.method,
        timestamp: Date.now()
      });

      // حظر طلبات API الخارجية للمعالجة
      const blockedDomains = [
        'api.huggingface.co',
        'api.openai.com',
        'generativelanguage.googleapis.com'
      ];

      if (blockedDomains.some(domain => req.url.includes(domain)) && req.method === 'POST') {
        req.reply({ statusCode: 403, body: 'محظور: استخدم المعالجة المحلية' });
      }
    });

    // تسجيل الدخول كتاجر للاختبار
    cy.login('merchant');
    cy.visit('/ai-document-processing');
  });

  describe('واجهة رفع المستندات المحلية', () => {
    it('يجب أن تعرض واجهة رفع المستندات مع مؤشرات المعالجة المحلية', () => {
      // التحقق من وجود العناصر الأساسية
      cy.contains('نظام معالجة المستندات الذكي').should('be.visible');
      cy.contains('ارفع مستنداتك واحصل على موافقة تلقائية').should('be.visible');

      // التحقق من مؤشرات المعالجة المحلية
      cy.contains('معالجة محلية آمنة').should('be.visible');
      cy.contains('بدون تكاليف إضافية').should('be.visible');

      // التحقق من منطقة السحب والإفلات
      cy.get('[data-testid="dropzone"]').should('be.visible');
      cy.contains('اسحب الملفات هنا أو انقر للاختيار').should('be.visible');

      // التحقق من معلومات الصيغ المدعومة
      cy.contains('الصيغ المدعومة: JPG, PNG, WebP, HEIC, PDF').should('be.visible');
      cy.contains('الحد الأقصى: 10 ميجابايت').should('be.visible');

      // التحقق من تبويب النماذج المحلية
      cy.get('[data-testid="models-tab"]').should('be.visible');
      cy.contains('النماذج المحلية').should('be.visible');
    });

    it('يجب أن يرفض الملفات غير المدعومة', () => {
      // محاولة رفع ملف بصيغة غير مدعومة
      const unsupportedFile = new File(['test'], 'test.txt', { type: 'text/plain' });
      
      cy.get('[data-testid="file-input"]').selectFile(unsupportedFile, { force: true });
      
      // التحقق من ظهور رسالة خطأ
      cy.contains('نوع الملف test.txt غير مدعوم').should('be.visible');
    });

    it('يجب أن يرفض الملفات الكبيرة جداً', () => {
      // إنشاء ملف كبير (محاكاة)
      const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.jpg', { 
        type: 'image/jpeg' 
      });
      
      cy.get('[data-testid="file-input"]').selectFile(largeFile, { force: true });
      
      // التحقق من ظهور رسالة خطأ
      cy.contains('الملف large.jpg كبير جداً').should('be.visible');
    });

    it('يجب أن يقبل الملفات الصالحة ويعرضها', () => {
      // رفع ملف صالح
      cy.fixture('sample-document.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'document.jpg', {
          type: 'image/jpeg'
        });
        
        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        
        // التحقق من ظهور الملف في القائمة
        cy.get('[data-testid="uploaded-files"]').should('be.visible');
        cy.contains('document.jpg').should('be.visible');
        cy.get('[data-testid="upload-button"]').should('be.visible');
      });
    });
  });

  describe('عملية رفع ومعالجة المستندات', () => {
    beforeEach(() => {
      // إعداد اعتراض API calls
      cy.intercept('POST', '/api/ai-document-processing/upload', {
        statusCode: 200,
        body: {
          success: true,
          data: {
            processingId: 'test-processing-id-123',
            status: 'processing',
            currentStage: 'ocr',
            estimatedTime: '2-3 دقائق'
          }
        }
      }).as('uploadDocument');

      cy.intercept('GET', '/api/ai-document-processing/status/test-processing-id-123', {
        statusCode: 200,
        body: {
          success: true,
          data: {
            id: 'test-processing-id-123',
            status: 'completed',
            currentStage: 'completed',
            overallConfidence: 0.85,
            stages: {
              ocr: {
                completed: true,
                confidence: 0.88,
                processingTime: 1200,
                modelUsed: 'microsoft/trocr-base-printed'
              },
              ner: {
                completed: true,
                confidence: 0.82,
                processingTime: 800,
                modelUsed: 'aubmindlab/bert-base-arabertv02'
              },
              classification: {
                completed: true,
                confidence: 0.85,
                processingTime: 600,
                modelUsed: 'microsoft/DialoGPT-large'
              }
            },
            results: {
              decision: 'approve',
              documentType: 'commercial_registration',
              extractedData: {
                businessName: 'شركة الاختبار التجارية',
                ownerName: 'أحمد محمد علي',
                registrationNumber: '1234567890'
              },
              reasons: ['جميع البيانات المطلوبة متوفرة وصحيحة'],
              riskScore: 15
            },
            qualityCheck: {
              imageQuality: 0.9,
              blurDetected: false,
              resolutionSufficient: true,
              formatSupported: true
            },
            errors: [],
            warnings: [],
            fileInfo: {
              originalFileName: 'commercial-registration.jpg',
              fileSize: 2048576,
              mimeType: 'image/jpeg'
            }
          }
        }
      }).as('getProcessingStatus');
    });

    it('يجب أن يرفع المستند ويبدأ المعالجة', () => {
      // رفع ملف
      cy.fixture('sample-document.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'document.jpg', {
          type: 'image/jpeg'
        });
        
        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="upload-button"]').click();
        
        // التحقق من استدعاء API
        cy.wait('@uploadDocument');
        
        // التحقق من ظهور حالة المعالجة
        cy.contains('جاري المعالجة...').should('be.visible');
        cy.get('[data-testid="progress-bar"]').should('be.visible');
      });
    });

    it('يجب أن يعرض النتائج بعد اكتمال المعالجة', () => {
      // محاكاة رفع ناجح
      cy.get('[data-testid="mock-upload-success"]').click();
      
      // انتظار تحديث الحالة
      cy.wait('@getProcessingStatus');
      
      // التحقق من عرض النتائج
      cy.contains('موافق عليه').should('be.visible');
      cy.contains('commercial_registration').should('be.visible');
      cy.contains('85%').should('be.visible'); // الثقة الإجمالية
      
      // التحقق من البيانات المستخلصة
      cy.get('[data-testid="extracted-data"]').should('be.visible');
      cy.contains('شركة الاختبار التجارية').should('be.visible');
      cy.contains('أحمد محمد علي').should('be.visible');
      cy.contains('1234567890').should('be.visible');
    });

    it('يجب أن يعرض تفاصيل المراحل', () => {
      cy.get('[data-testid="mock-upload-success"]').click();
      cy.wait('@getProcessingStatus');
      
      // النقر على تبويب المراحل
      cy.get('[data-testid="stages-tab"]').click();
      
      // التحقق من عرض تفاصيل كل مرحلة
      cy.contains('استخراج النص من المستند').should('be.visible');
      cy.contains('استخلاص البيانات والمعلومات').should('be.visible');
      cy.contains('تصنيف المستند واتخاذ القرار').should('be.visible');
      
      // التحقق من أيقونات الإكمال
      cy.get('[data-testid="stage-completed"]').should('have.length', 3);
    });

    it('يجب أن يعرض تقرير الجودة', () => {
      cy.get('[data-testid="mock-upload-success"]').click();
      cy.wait('@getProcessingStatus');
      
      // النقر على تبويب الجودة
      cy.get('[data-testid="quality-tab"]').click();
      
      // التحقق من معلومات الجودة
      cy.contains('جودة الصورة').should('be.visible');
      cy.contains('90%').should('be.visible'); // جودة الصورة
      cy.contains('الدقة كافية').should('be.visible');
      cy.contains('التنسيق مدعوم').should('be.visible');
    });
  });

  describe('معالجة الأخطاء والحالات الاستثنائية', () => {
    it('يجب أن يتعامل مع فشل الرفع', () => {
      cy.intercept('POST', '/api/ai-document-processing/upload', {
        statusCode: 500,
        body: {
          success: false,
          error: 'خطأ في الخادم'
        }
      }).as('uploadError');

      cy.fixture('sample-document.jpg', 'base64').then(fileContent => {
        const file = new File([Cypress.Blob.base64StringToBlob(fileContent)], 'document.jpg', {
          type: 'image/jpeg'
        });
        
        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="upload-button"]').click();
        
        cy.wait('@uploadError');
        
        // التحقق من ظهور رسالة الخطأ
        cy.contains('خطأ في الخادم').should('be.visible');
        cy.get('[data-testid="retry-button"]').should('be.visible');
      });
    });

    it('يجب أن يتعامل مع المستندات التي تتطلب إعادة رفع', () => {
      cy.intercept('GET', '/api/ai-document-processing/status/*', {
        statusCode: 200,
        body: {
          success: true,
          data: {
            status: 'requires_reupload',
            qualityCheck: {
              imageQuality: 0.3,
              blurDetected: true,
              resolutionSufficient: false,
              formatSupported: true
            },
            errors: ['جودة الملف غير كافية أو التنسيق غير مدعوم']
          }
        }
      }).as('requiresReupload');

      cy.get('[data-testid="mock-upload-success"]').click();
      cy.wait('@requiresReupload');
      
      // التحقق من ظهور رسالة إعادة الرفع
      cy.contains('يتطلب إعادة رفع المستند').should('be.visible');
      cy.contains('جودة المستند غير كافية').should('be.visible');
      cy.get('[data-testid="reupload-button"]').should('be.visible');
    });

    it('يجب أن يتعامل مع المستندات المرفوضة', () => {
      cy.intercept('GET', '/api/ai-document-processing/status/*', {
        statusCode: 200,
        body: {
          success: true,
          data: {
            status: 'completed',
            results: {
              decision: 'reject',
              documentType: 'commercial_registration',
              reasons: [
                'اسم المنشأة مفقود',
                'رقم السجل التجاري مفقود',
                'نقاط المخاطر عالية جداً'
              ],
              riskScore: 85
            }
          }
        }
      }).as('rejectedDocument');

      cy.get('[data-testid="mock-upload-success"]').click();
      cy.wait('@rejectedDocument');
      
      // التحقق من عرض حالة الرفض
      cy.contains('مرفوض').should('be.visible');
      cy.contains('اسم المنشأة مفقود').should('be.visible');
      cy.contains('نقاط المخاطر عالية جداً').should('be.visible');
    });
  });

  describe('تاريخ المعالجات', () => {
    beforeEach(() => {
      cy.intercept('GET', '/api/ai-document-processing/history*', {
        statusCode: 200,
        body: {
          success: true,
          data: {
            documents: [
              {
                id: 'doc1',
                status: 'completed',
                originalFileName: 'commercial-reg.jpg',
                fileSize: 2048576,
                overallConfidence: 0.85,
                results: {
                  decision: 'approve',
                  documentType: 'commercial_registration'
                },
                createdAt: new Date().toISOString(),
                processingStats: {
                  totalTime: 2600
                }
              },
              {
                id: 'doc2',
                status: 'processing',
                originalFileName: 'id-card.jpg',
                fileSize: 1024768,
                overallConfidence: 0,
                createdAt: new Date().toISOString(),
                processingStats: {
                  totalTime: 0
                }
              }
            ],
            stats: {
              total: 2,
              completed: 1,
              processing: 1,
              failed: 0,
              requiresReupload: 0,
              averageConfidence: 0.425,
              averageProcessingTime: 1300
            }
          }
        }
      }).as('getHistory');
    });

    it('يجب أن يعرض تاريخ المعالجات', () => {
      cy.visit('/ai-document-processing/history');
      cy.wait('@getHistory');
      
      // التحقق من الإحصائيات
      cy.contains('2').should('be.visible'); // إجمالي المستندات
      cy.contains('1').should('be.visible'); // مكتملة
      cy.contains('43%').should('be.visible'); // متوسط الثقة
      
      // التحقق من جدول التاريخ
      cy.contains('commercial-reg.jpg').should('be.visible');
      cy.contains('id-card.jpg').should('be.visible');
      cy.contains('موافق').should('be.visible');
      cy.contains('قيد المعالجة').should('be.visible');
    });

    it('يجب أن يفلتر النتائج حسب الحالة', () => {
      cy.visit('/ai-document-processing/history');
      cy.wait('@getHistory');
      
      // اختيار فلتر "مكتمل"
      cy.get('[data-testid="status-filter"]').click();
      cy.contains('مكتمل').click();
      
      // التحقق من تحديث النتائج
      cy.contains('commercial-reg.jpg').should('be.visible');
      cy.contains('id-card.jpg').should('not.exist');
    });

    it('يجب أن يبحث في أسماء الملفات', () => {
      cy.visit('/ai-document-processing/history');
      cy.wait('@getHistory');
      
      // البحث عن ملف محدد
      cy.get('[data-testid="search-input"]').type('commercial');
      
      // التحقق من النتائج المفلترة
      cy.contains('commercial-reg.jpg').should('be.visible');
      cy.contains('id-card.jpg').should('not.exist');
    });
  });

  describe('التبديل بين اللغات', () => {
    it('يجب أن يعرض المحتوى بالإنجليزية عند التبديل', () => {
      // التبديل للإنجليزية
      cy.get('[data-testid="language-switcher"]').click();
      cy.contains('English').click();
      
      // التحقق من تغيير النصوص
      cy.contains('AI Document Processing System').should('be.visible');
      cy.contains('Upload your documents').should('be.visible');
      cy.contains('Supported formats: JPG, PNG, WebP, HEIC, PDF').should('be.visible');
    });

    it('يجب أن يحافظ على اللغة المختارة عند التنقل', () => {
      // التبديل للإنجليزية
      cy.get('[data-testid="language-switcher"]').click();
      cy.contains('English').click();
      
      // الانتقال لصفحة أخرى
      cy.visit('/ai-document-processing/history');
      
      // التحقق من استمرار اللغة الإنجليزية
      cy.contains('Processing History').should('be.visible');
    });
  });

  describe('الاستجابة للأجهزة المختلفة', () => {
    it('يجب أن يعمل على الأجهزة المحمولة', () => {
      cy.viewport('iphone-x');
      
      // التحقق من تكيف التصميم
      cy.get('[data-testid="dropzone"]').should('be.visible');
      cy.get('[data-testid="mobile-menu"]').should('be.visible');
      
      // التحقق من إمكانية التفاعل
      cy.get('[data-testid="dropzone"]').click();
    });

    it('يجب أن يعمل على الأجهزة اللوحية', () => {
      cy.viewport('ipad-2');
      
      // التحقق من التصميم المتجاوب
      cy.get('[data-testid="dropzone"]').should('be.visible');
      cy.get('.grid').should('have.class', 'md:grid-cols-3');
    });
  });
});
