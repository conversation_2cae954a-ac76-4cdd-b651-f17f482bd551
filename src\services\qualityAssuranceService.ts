// src/services/qualityAssuranceService.ts - نظام ضمان الجودة المتقدم
import { ProcessingResult } from './huggingFaceAIService';

// معايير الجودة
const QUALITY_STANDARDS = {
  image: {
    minResolution: 300, // DPI
    minWidth: 800,
    minHeight: 600,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    supportedFormats: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic'],
    maxBlurThreshold: 0.3,
    minBrightnessThreshold: 0.2,
    maxBrightnessThreshold: 0.9
  },
  pdf: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxPages: 10,
    minTextDensity: 0.1 // نسبة النص إلى المساحة
  },
  processing: {
    minOCRConfidence: 0.7,
    minNERConfidence: 0.6,
    minClassificationConfidence: 0.8,
    minOverallConfidence: 0.7,
    maxProcessingTime: 300000 // 5 دقائق
  },
  content: {
    minTextLength: 10,
    maxStrangeCharRatio: 0.1,
    minUniqueWordRatio: 0.3,
    requiredFieldsThreshold: 0.8
  }
};

// أنواع مشاكل الجودة
export interface QualityIssue {
  type: 'critical' | 'warning' | 'info';
  category: 'image' | 'content' | 'processing' | 'data';
  code: string;
  message: string;
  suggestion: string;
  autoFixable: boolean;
}

export interface QualityReport {
  overallScore: number; // 0-100
  passed: boolean;
  issues: QualityIssue[];
  recommendations: string[];
  requiresReupload: boolean;
  canProceed: boolean;
  
  // تفاصيل الفحص
  imageQuality: {
    score: number;
    resolution: number;
    blur: number;
    brightness: number;
    contrast: number;
  };
  
  contentQuality: {
    score: number;
    textLength: number;
    strangeCharRatio: number;
    uniqueWordRatio: number;
    languageDetected: string;
  };
  
  processingQuality: {
    score: number;
    ocrConfidence: number;
    nerConfidence: number;
    classificationConfidence: number;
    processingTime: number;
  };
  
  dataQuality: {
    score: number;
    requiredFieldsFound: number;
    totalRequiredFields: number;
    dataConsistency: number;
  };
}

export class QualityAssuranceService {
  private static instance: QualityAssuranceService;
  
  private constructor() {}
  
  static getInstance(): QualityAssuranceService {
    if (!QualityAssuranceService.instance) {
      QualityAssuranceService.instance = new QualityAssuranceService();
    }
    return QualityAssuranceService.instance;
  }

  /**
   * فحص شامل لجودة المعالجة
   */
  async assessQuality(
    processingResult: ProcessingResult,
    originalFileInfo: {
      url: string;
      size: number;
      mimeType: string;
      name: string;
    }
  ): Promise<QualityReport> {
    console.log('🔍 بدء فحص ضمان الجودة الشامل...');
    
    const issues: QualityIssue[] = [];
    const recommendations: string[] = [];
    
    // فحص جودة الصورة/الملف
    const imageQuality = await this.assessImageQuality(originalFileInfo);
    issues.push(...imageQuality.issues);
    
    // فحص جودة المحتوى المستخرج
    const contentQuality = await this.assessContentQuality(processingResult);
    issues.push(...contentQuality.issues);
    
    // فحص جودة المعالجة
    const processingQuality = await this.assessProcessingQuality(processingResult);
    issues.push(...processingQuality.issues);
    
    // فحص جودة البيانات المستخلصة
    const dataQuality = await this.assessDataQuality(processingResult);
    issues.push(...dataQuality.issues);
    
    // حساب النقاط الإجمالية
    const overallScore = this.calculateOverallScore(
      imageQuality.score,
      contentQuality.score,
      processingQuality.score,
      dataQuality.score
    );
    
    // تحديد ما إذا كان يتطلب إعادة رفع
    const requiresReupload = this.shouldRequireReupload(issues, overallScore);
    
    // تحديد ما إذا كان يمكن المتابعة
    const canProceed = this.canProceedWithProcessing(issues, overallScore);
    
    // توليد التوصيات
    recommendations.push(...this.generateRecommendations(issues, overallScore));
    
    const report: QualityReport = {
      overallScore,
      passed: overallScore >= 70,
      issues,
      recommendations,
      requiresReupload,
      canProceed,
      imageQuality: imageQuality.details,
      contentQuality: contentQuality.details,
      processingQuality: processingQuality.details,
      dataQuality: dataQuality.details
    };
    
    console.log(`✅ اكتمل فحص الجودة. النقاط: ${overallScore}/100`);
    return report;
  }

  /**
   * فحص جودة الصورة/الملف
   */
  private async assessImageQuality(fileInfo: any) {
    const issues: QualityIssue[] = [];
    let score = 100;
    
    // فحص حجم الملف
    if (fileInfo.size > QUALITY_STANDARDS.image.maxFileSize) {
      issues.push({
        type: 'critical',
        category: 'image',
        code: 'FILE_TOO_LARGE',
        message: 'حجم الملف كبير جداً',
        suggestion: 'قم بضغط الصورة أو تقليل جودتها',
        autoFixable: false
      });
      score -= 30;
    }
    
    // فحص نوع الملف
    if (!QUALITY_STANDARDS.image.supportedFormats.includes(fileInfo.mimeType)) {
      issues.push({
        type: 'critical',
        category: 'image',
        code: 'UNSUPPORTED_FORMAT',
        message: 'نوع الملف غير مدعوم',
        suggestion: 'استخدم صيغة JPG أو PNG أو WebP',
        autoFixable: false
      });
      score -= 50;
    }
    
    // محاكاة فحص جودة الصورة (في التطبيق الحقيقي، استخدم مكتبة معالجة الصور)
    const mockImageAnalysis = {
      resolution: 300,
      blur: 0.2,
      brightness: 0.6,
      contrast: 0.7
    };
    
    if (mockImageAnalysis.blur > QUALITY_STANDARDS.image.maxBlurThreshold) {
      issues.push({
        type: 'warning',
        category: 'image',
        code: 'IMAGE_BLURRY',
        message: 'الصورة غير واضحة',
        suggestion: 'التقط صورة أوضح مع تركيز أفضل',
        autoFixable: false
      });
      score -= 20;
    }
    
    if (mockImageAnalysis.brightness < QUALITY_STANDARDS.image.minBrightnessThreshold) {
      issues.push({
        type: 'warning',
        category: 'image',
        code: 'IMAGE_TOO_DARK',
        message: 'الصورة مظلمة جداً',
        suggestion: 'التقط الصورة في إضاءة أفضل',
        autoFixable: true
      });
      score -= 15;
    }
    
    return {
      score: Math.max(score, 0),
      issues,
      details: {
        score: Math.max(score, 0),
        resolution: mockImageAnalysis.resolution,
        blur: mockImageAnalysis.blur,
        brightness: mockImageAnalysis.brightness,
        contrast: mockImageAnalysis.contrast
      }
    };
  }

  /**
   * فحص جودة المحتوى المستخرج
   */
  private async assessContentQuality(processingResult: ProcessingResult) {
    const issues: QualityIssue[] = [];
    let score = 100;
    
    const extractedText = processingResult.ocrResult?.extractedText || '';
    
    // فحص طول النص
    if (extractedText.length < QUALITY_STANDARDS.content.minTextLength) {
      issues.push({
        type: 'critical',
        category: 'content',
        code: 'TEXT_TOO_SHORT',
        message: 'النص المستخرج قصير جداً',
        suggestion: 'تأكد من وضوح النص في الصورة',
        autoFixable: false
      });
      score -= 40;
    }
    
    // فحص الأحرف الغريبة
    const strangeCharsRegex = /[^\u0600-\u06FF\u0020-\u007E\s\d]/g;
    const strangeChars = extractedText.match(strangeCharsRegex) || [];
    const strangeCharRatio = strangeChars.length / extractedText.length;
    
    if (strangeCharRatio > QUALITY_STANDARDS.content.maxStrangeCharRatio) {
      issues.push({
        type: 'warning',
        category: 'content',
        code: 'TOO_MANY_STRANGE_CHARS',
        message: 'يحتوي النص على أحرف غريبة كثيرة',
        suggestion: 'تحقق من جودة الصورة ووضوح النص',
        autoFixable: true
      });
      score -= 20;
    }
    
    // فحص تنوع الكلمات
    const words = extractedText.split(/\s+/).filter(w => w.length > 2);
    const uniqueWords = new Set(words);
    const uniqueWordRatio = words.length > 0 ? uniqueWords.size / words.length : 0;
    
    if (uniqueWordRatio < QUALITY_STANDARDS.content.minUniqueWordRatio) {
      issues.push({
        type: 'info',
        category: 'content',
        code: 'LOW_WORD_DIVERSITY',
        message: 'تكرار مفرط في الكلمات',
        suggestion: 'قد يشير إلى خطأ في استخراج النص',
        autoFixable: false
      });
      score -= 10;
    }
    
    return {
      score: Math.max(score, 0),
      issues,
      details: {
        score: Math.max(score, 0),
        textLength: extractedText.length,
        strangeCharRatio,
        uniqueWordRatio,
        languageDetected: processingResult.ocrResult?.language || 'unknown'
      }
    };
  }

  /**
   * فحص جودة المعالجة
   */
  private async assessProcessingQuality(processingResult: ProcessingResult) {
    const issues: QualityIssue[] = [];
    let score = 100;
    
    // فحص ثقة OCR
    const ocrConfidence = processingResult.ocrResult?.confidence || 0;
    if (ocrConfidence < QUALITY_STANDARDS.processing.minOCRConfidence) {
      issues.push({
        type: 'warning',
        category: 'processing',
        code: 'LOW_OCR_CONFIDENCE',
        message: 'ثقة منخفضة في استخراج النص',
        suggestion: 'قد تحتاج لصورة أوضح',
        autoFixable: false
      });
      score -= 25;
    }
    
    // فحص ثقة NER
    const nerConfidence = processingResult.nerResult?.confidence || 0;
    if (nerConfidence < QUALITY_STANDARDS.processing.minNERConfidence) {
      issues.push({
        type: 'warning',
        category: 'processing',
        code: 'LOW_NER_CONFIDENCE',
        message: 'ثقة منخفضة في استخلاص البيانات',
        suggestion: 'قد يتطلب مراجعة يدوية',
        autoFixable: false
      });
      score -= 20;
    }
    
    // فحص ثقة التصنيف
    const classificationConfidence = processingResult.classificationResult?.confidence || 0;
    if (classificationConfidence < QUALITY_STANDARDS.processing.minClassificationConfidence) {
      issues.push({
        type: 'info',
        category: 'processing',
        code: 'LOW_CLASSIFICATION_CONFIDENCE',
        message: 'ثقة منخفضة في تصنيف المستند',
        suggestion: 'قد يتطلب تأكيد نوع المستند',
        autoFixable: false
      });
      score -= 15;
    }
    
    return {
      score: Math.max(score, 0),
      issues,
      details: {
        score: Math.max(score, 0),
        ocrConfidence,
        nerConfidence,
        classificationConfidence,
        processingTime: (processingResult.ocrResult?.processingTime || 0) +
                       (processingResult.nerResult?.processingTime || 0) +
                       (processingResult.classificationResult?.processingTime || 0)
      }
    };
  }

  /**
   * فحص جودة البيانات المستخلصة
   */
  private async assessDataQuality(processingResult: ProcessingResult) {
    const issues: QualityIssue[] = [];
    let score = 100;
    
    const extractedData = processingResult.nerResult?.extractedData || {};
    const userType = processingResult.userType;
    
    // تحديد الحقول المطلوبة حسب نوع المستخدم
    const requiredFields = userType === 'merchant' 
      ? ['businessName', 'ownerName', 'registrationNumber']
      : ['ownerName', 'nationalId', 'drivingLicense'];
    
    const foundFields = requiredFields.filter(field => 
      extractedData[field] && extractedData[field].toString().trim()
    );
    
    const foundRatio = foundFields.length / requiredFields.length;
    
    if (foundRatio < QUALITY_STANDARDS.content.requiredFieldsThreshold) {
      issues.push({
        type: 'critical',
        category: 'data',
        code: 'MISSING_REQUIRED_FIELDS',
        message: 'حقول مطلوبة مفقودة',
        suggestion: 'تأكد من وضوح جميع البيانات في المستند',
        autoFixable: false
      });
      score -= 40;
    }
    
    return {
      score: Math.max(score, 0),
      issues,
      details: {
        score: Math.max(score, 0),
        requiredFieldsFound: foundFields.length,
        totalRequiredFields: requiredFields.length,
        dataConsistency: foundRatio
      }
    };
  }

  /**
   * حساب النقاط الإجمالية
   */
  private calculateOverallScore(
    imageScore: number,
    contentScore: number,
    processingScore: number,
    dataScore: number
  ): number {
    // أوزان مختلفة للجوانب المختلفة
    const weights = {
      image: 0.2,
      content: 0.3,
      processing: 0.2,
      data: 0.3
    };
    
    return Math.round(
      imageScore * weights.image +
      contentScore * weights.content +
      processingScore * weights.processing +
      dataScore * weights.data
    );
  }

  /**
   * تحديد ما إذا كان يتطلب إعادة رفع
   */
  private shouldRequireReupload(issues: QualityIssue[], overallScore: number): boolean {
    const criticalIssues = issues.filter(issue => issue.type === 'critical');
    return criticalIssues.length > 0 || overallScore < 50;
  }

  /**
   * تحديد ما إذا كان يمكن المتابعة
   */
  private canProceedWithProcessing(issues: QualityIssue[], overallScore: number): boolean {
    const criticalIssues = issues.filter(issue => issue.type === 'critical');
    return criticalIssues.length === 0 && overallScore >= 60;
  }

  /**
   * توليد التوصيات
   */
  private generateRecommendations(issues: QualityIssue[], overallScore: number): string[] {
    const recommendations: string[] = [];
    
    if (overallScore < 70) {
      recommendations.push('ننصح بتحسين جودة الصورة قبل إعادة الرفع');
    }
    
    const imageIssues = issues.filter(issue => issue.category === 'image');
    if (imageIssues.length > 0) {
      recommendations.push('تأكد من التقاط الصورة في إضاءة جيدة ومع تركيز واضح');
    }
    
    const contentIssues = issues.filter(issue => issue.category === 'content');
    if (contentIssues.length > 0) {
      recommendations.push('تأكد من أن النص في المستند واضح ومقروء');
    }
    
    return recommendations;
  }
}
