'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>ircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  Eye,
  Download,
  RefreshCw,
  TrendingUp,
  Shield,
  Zap
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ProcessingResultsProps {
  processingId: string;
  onReuploadRequest: () => void;
}

interface ProcessingData {
  id: string;
  status: 'processing' | 'completed' | 'failed' | 'requires_reupload';
  currentStage: 'ocr' | 'ner' | 'classification' | 'completed';
  overallConfidence: number;
  
  stages: {
    ocr: {
      completed: boolean;
      confidence: number;
      processingTime: number;
      modelUsed: string;
    };
    ner: {
      completed: boolean;
      confidence: number;
      processingTime: number;
      modelUsed: string;
    };
    classification: {
      completed: boolean;
      confidence: number;
      processingTime: number;
      modelUsed: string;
    };
  };

  results?: {
    decision: 'approve' | 'reject' | 'manual_review';
    documentType: string;
    extractedData: Record<string, any>;
    reasons: string[];
    riskScore: number;
  };

  qualityCheck: {
    imageQuality: number;
    blurDetected: boolean;
    resolutionSufficient: boolean;
    formatSupported: boolean;
  };

  errors: string[];
  warnings: string[];
  fileInfo: {
    originalFileName: string;
    fileSize: number;
    mimeType: string;
  };
}

export default function ProcessingResults({ 
  processingId, 
  onReuploadRequest 
}: ProcessingResultsProps) {
  const [data, setData] = useState<ProcessingData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProcessingStatus = async () => {
    try {
      const response = await fetch(`/api/ai-document-processing/status/${processingId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('فشل في جلب حالة المعالجة');
      }

      const result = await response.json();
      setData(result.data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ غير معروف');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProcessingStatus();
    
    // تحديث دوري للحالة إذا كانت المعالجة جارية
    const interval = setInterval(() => {
      if (data?.status === 'processing') {
        fetchProcessingStatus();
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [processingId, data?.status]);

  const getDecisionIcon = (decision: string) => {
    switch (decision) {
      case 'approve':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      case 'reject':
        return <XCircle className="h-6 w-6 text-red-500" />;
      case 'manual_review':
        return <Clock className="h-6 w-6 text-yellow-500" />;
      default:
        return <AlertTriangle className="h-6 w-6 text-gray-500" />;
    }
  };

  const getDecisionText = (decision: string) => {
    switch (decision) {
      case 'approve':
        return 'موافق عليه';
      case 'reject':
        return 'مرفوض';
      case 'manual_review':
        return 'يتطلب مراجعة يدوية';
      default:
        return 'غير محدد';
    }
  };

  const getDecisionColor = (decision: string) => {
    switch (decision) {
      case 'approve':
        return 'bg-green-100 text-green-800';
      case 'reject':
        return 'bg-red-100 text-red-800';
      case 'manual_review':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStageProgress = () => {
    if (!data) return 0;
    
    const stages = ['ocr', 'ner', 'classification'] as const;
    const completedStages = stages.filter(stage => data.stages[stage].completed).length;
    return (completedStages / stages.length) * 100;
  };

  const getCurrentStageText = (stage: string) => {
    switch (stage) {
      case 'ocr':
        return 'استخراج النص من المستند';
      case 'ner':
        return 'استخلاص البيانات والمعلومات';
      case 'classification':
        return 'تصنيف المستند واتخاذ القرار';
      case 'completed':
        return 'اكتملت المعالجة';
      default:
        return 'جاري المعالجة...';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            <span>جاري تحميل النتائج...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {error}
          <Button 
            variant="outline" 
            size="sm" 
            className="ml-2"
            onClick={fetchProcessingStatus}
          >
            إعادة المحاولة
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (!data) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          لم يتم العثور على بيانات المعالجة
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* مؤشر المعالجة المحلية */}
      <Card className="border-green-200 bg-green-50">
        <CardContent className="py-4">
          <div className="flex items-center justify-center gap-4 text-green-800">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              <span className="font-medium">معالجة محلية آمنة</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              <span className="font-medium">بدون تكاليف إضافية</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* حالة المعالجة العامة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              حالة معالجة المستند
            </span>
            <Badge variant="outline">
              {data.fileInfo.originalFileName}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {data.status === 'processing' ? (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <RefreshCw className="h-5 w-5 animate-spin text-blue-500" />
                <span className="font-medium">جاري المعالجة...</span>
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>التقدم الإجمالي</span>
                  <span>{Math.round(getStageProgress())}%</span>
                </div>
                <Progress value={getStageProgress()} />
              </div>
              
              <p className="text-sm text-gray-600">
                المرحلة الحالية: {getCurrentStageText(data.currentStage)}
              </p>
            </div>
          ) : data.status === 'completed' && data.results ? (
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                {getDecisionIcon(data.results.decision)}
                <div>
                  <p className="font-medium text-lg">
                    {getDecisionText(data.results.decision)}
                  </p>
                  <Badge className={getDecisionColor(data.results.decision)}>
                    نوع المستند: {data.results.documentType}
                  </Badge>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">الثقة الإجمالية:</span>
                  <span className="font-medium ml-2">
                    {Math.round(data.overallConfidence * 100)}%
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">نقاط المخاطر:</span>
                  <span className="font-medium ml-2">
                    {data.results.riskScore}/100
                  </span>
                </div>
              </div>
            </div>
          ) : data.status === 'requires_reupload' ? (
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-orange-600">
                <AlertTriangle className="h-5 w-5" />
                <span className="font-medium">يتطلب إعادة رفع المستند</span>
              </div>
              
              <Alert>
                <AlertDescription>
                  جودة المستند غير كافية للمعالجة. يرجى رفع صورة أوضح.
                </AlertDescription>
              </Alert>
              
              <Button onClick={onReuploadRequest} className="w-full">
                رفع مستند جديد
              </Button>
            </div>
          ) : (
            <div className="flex items-center gap-2 text-red-600">
              <XCircle className="h-5 w-5" />
              <span className="font-medium">فشلت المعالجة</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* تفاصيل المراحل والنتائج */}
      {data.status === 'completed' && (
        <Tabs defaultValue="results" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="results">النتائج</TabsTrigger>
            <TabsTrigger value="stages">المراحل</TabsTrigger>
            <TabsTrigger value="data">البيانات</TabsTrigger>
            <TabsTrigger value="quality">الجودة</TabsTrigger>
          </TabsList>

          <TabsContent value="results" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>نتائج التحليل</CardTitle>
              </CardHeader>
              <CardContent>
                {data.results && (
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      {getDecisionIcon(data.results.decision)}
                      <div>
                        <p className="font-medium text-lg">
                          {getDecisionText(data.results.decision)}
                        </p>
                        <p className="text-sm text-gray-600">
                          نوع المستند: {data.results.documentType}
                        </p>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">أسباب القرار:</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm">
                        {data.results.reasons.map((reason, index) => (
                          <li key={index}>{reason}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="stages" className="space-y-4">
            <div className="grid gap-4">
              {Object.entries(data.stages).map(([stageName, stage]) => (
                <Card key={stageName}>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {stage.completed ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <Clock className="h-5 w-5 text-gray-400" />
                        )}
                        <span className="font-medium">
                          {getCurrentStageText(stageName)}
                        </span>
                      </div>
                      
                      {stage.completed && (
                        <div className="text-right text-sm">
                          <p>الثقة: {Math.round(stage.confidence * 100)}%</p>
                          <p className="text-gray-500">
                            {stage.processingTime}ms
                          </p>
                        </div>
                      )}
                    </div>
                    
                    {stage.completed && (
                      <p className="text-xs text-gray-500 mt-2">
                        النموذج: {stage.modelUsed}
                      </p>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="data" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>البيانات المستخلصة</CardTitle>
              </CardHeader>
              <CardContent>
                {data.results?.extractedData && (
                  <div className="grid gap-3">
                    {Object.entries(data.results.extractedData).map(([key, value]) => (
                      <div key={key} className="flex justify-between py-2 border-b">
                        <span className="font-medium text-gray-600">
                          {key}:
                        </span>
                        <span className="text-right">
                          {value?.toString() || 'غير متوفر'}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="quality" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>تقرير الجودة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  <div className="flex justify-between items-center">
                    <span>جودة الصورة:</span>
                    <div className="flex items-center gap-2">
                      <Progress 
                        value={data.qualityCheck.imageQuality * 100} 
                        className="w-20" 
                      />
                      <span className="text-sm">
                        {Math.round(data.qualityCheck.imageQuality * 100)}%
                      </span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex justify-between">
                      <span>الدقة كافية:</span>
                      <Badge variant={data.qualityCheck.resolutionSufficient ? "default" : "destructive"}>
                        {data.qualityCheck.resolutionSufficient ? 'نعم' : 'لا'}
                      </Badge>
                    </div>
                    
                    <div className="flex justify-between">
                      <span>التنسيق مدعوم:</span>
                      <Badge variant={data.qualityCheck.formatSupported ? "default" : "destructive"}>
                        {data.qualityCheck.formatSupported ? 'نعم' : 'لا'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* الأخطاء والتحذيرات */}
      {(data.errors.length > 0 || data.warnings.length > 0) && (
        <div className="space-y-4">
          {data.errors.length > 0 && (
            <Alert>
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>أخطاء:</strong>
                <ul className="list-disc list-inside mt-2">
                  {data.errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
          
          {data.warnings.length > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>تحذيرات:</strong>
                <ul className="list-disc list-inside mt-2">
                  {data.warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}
    </div>
  );
}
