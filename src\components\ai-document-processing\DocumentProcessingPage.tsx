'use client';

import React, { useState } from 'react';
import { Brain, FileText, Zap, Shield, Clock, TrendingUp } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import DocumentUploader from './DocumentUploader';
import ProcessingResults from './ProcessingResults';
import LocalModelStatus from './LocalModelStatus';

interface DocumentProcessingPageProps {
  userType: 'merchant' | 'representative';
}

export default function DocumentProcessingPage({ userType }: DocumentProcessingPageProps) {
  const [activeProcessingId, setActiveProcessingId] = useState<string | null>(null);
  const [showUploader, setShowUploader] = useState(true);

  const handleUploadComplete = (processingId: string) => {
    setActiveProcessingId(processingId);
    setShowUploader(false);
  };

  const handleUploadError = (error: string) => {
    console.error('خطأ في الرفع:', error);
    // يمكن إضافة toast notification هنا
  };

  const handleReuploadRequest = () => {
    setActiveProcessingId(null);
    setShowUploader(true);
  };

  const getDocumentTypes = () => {
    if (userType === 'merchant') {
      return [
        { type: 'commercial_registration', name: 'السجل التجاري', required: true },
        { type: 'freelance_document', name: 'وثيقة العمل الحر', required: false },
        { type: 'national_id', name: 'الهوية الوطنية', required: true }
      ];
    } else {
      return [
        { type: 'national_id', name: 'الهوية الوطنية', required: true },
        { type: 'driving_license', name: 'رخصة القيادة', required: true },
        { type: 'vehicle_inspection', name: 'شهادة الفحص الدوري', required: true }
      ];
    }
  };

  const getProcessingSteps = () => [
    {
      icon: <FileText className="h-6 w-6" />,
      title: 'استخراج النص',
      description: 'تحويل الصور والمستندات إلى نص قابل للقراءة باستخدام تقنيات OCR المتقدمة'
    },
    {
      icon: <Brain className="h-6 w-6" />,
      title: 'استخلاص البيانات',
      description: 'تحليل النص واستخراج المعلومات المهمة مثل الأسماء والأرقام والتواريخ'
    },
    {
      icon: <Zap className="h-6 w-6" />,
      title: 'اتخاذ القرار',
      description: 'تصنيف المستند واتخاذ قرار الموافقة أو الرفض بناءً على قواعد العمل'
    }
  ];

  const getFeatures = () => [
    {
      icon: <Shield className="h-5 w-5 text-green-500" />,
      title: 'أمان عالي',
      description: 'معالجة آمنة للمستندات مع حماية البيانات الشخصية'
    },
    {
      icon: <Clock className="h-5 w-5 text-blue-500" />,
      title: 'معالجة سريعة',
      description: 'نتائج فورية خلال دقائق معدودة'
    },
    {
      icon: <TrendingUp className="h-5 w-5 text-purple-500" />,
      title: 'دقة عالية',
      description: 'تقنيات ذكاء اصطناعي متقدمة لضمان دقة النتائج'
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* العنوان الرئيسي */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          نظام معالجة المستندات الذكي
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          ارفع مستنداتك واحصل على موافقة تلقائية ذكية خلال دقائق باستخدام 
          أحدث تقنيات الذكاء الاصطناعي
        </p>
      </div>

      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="upload">رفع المستندات</TabsTrigger>
          <TabsTrigger value="models">النماذج المحلية</TabsTrigger>
          <TabsTrigger value="process">كيف يعمل النظام</TabsTrigger>
          <TabsTrigger value="requirements">المتطلبات</TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-6">
          {showUploader ? (
            <DocumentUploader
              userType={userType}
              onUploadComplete={handleUploadComplete}
              onUploadError={handleUploadError}
            />
          ) : activeProcessingId ? (
            <ProcessingResults
              processingId={activeProcessingId}
              onReuploadRequest={handleReuploadRequest}
            />
          ) : null}

          {/* الميزات */}
          <div className="grid md:grid-cols-3 gap-6 mt-8">
            {getFeatures().map((feature, index) => (
              <Card key={index}>
                <CardContent className="pt-6">
                  <div className="flex items-center gap-3 mb-3">
                    {feature.icon}
                    <h3 className="font-semibold">{feature.title}</h3>
                  </div>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="models" className="space-y-6">
          <LocalModelStatus
            onPreloadModels={async () => {
              // في التطبيق الحقيقي، سيتم استدعاء preloadModels من الخدمة
              console.log('تحميل النماذج المحلية...');
            }}
          />
        </TabsContent>

        <TabsContent value="process" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-6 w-6" />
                مراحل المعالجة الذكية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                {getProcessingSteps().map((step, index) => (
                  <div key={index} className="text-center">
                    <div className="flex justify-center mb-4">
                      <div className="p-3 bg-blue-100 rounded-full">
                        {step.icon}
                      </div>
                    </div>
                    <h3 className="font-semibold mb-2">{step.title}</h3>
                    <p className="text-sm text-gray-600">{step.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>التقنيات المستخدمة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3">نماذج الذكاء الاصطناعي المحلية</h4>
                  <ul className="space-y-2 text-sm">
                    <li>• TrOCR المحلي للنصوص المطبوعة والمكتوبة</li>
                    <li>• BERT متعدد اللغات للعربية والإنجليزية</li>
                    <li>• DistilBERT للتصنيف السريع</li>
                    <li>• معالجة محلية بدون تكاليف إضافية</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-3">ضمان الجودة</h4>
                  <ul className="space-y-2 text-sm">
                    <li>• فحص جودة الصورة تلقائياً</li>
                    <li>• التحقق من وضوح النص</li>
                    <li>• تقييم مستوى الثقة في النتائج</li>
                    <li>• إشعارات تلقائية للمشاكل</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="requirements" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>
                المستندات المطلوبة - {userType === 'merchant' ? 'التجار' : 'المندوبين'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {getDocumentTypes().map((doc, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{doc.name}</h4>
                      <p className="text-sm text-gray-600">
                        {doc.type}
                      </p>
                    </div>
                    <div className="text-right">
                      {doc.required ? (
                        <span className="text-red-600 font-medium">مطلوب</span>
                      ) : (
                        <span className="text-gray-500">اختياري</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>متطلبات جودة الصورة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3 text-green-600">✅ افعل</h4>
                  <ul className="space-y-2 text-sm">
                    <li>• التقط الصورة في إضاءة جيدة</li>
                    <li>• تأكد من وضوح النص</li>
                    <li>• استخدم دقة عالية (300 DPI أو أكثر)</li>
                    <li>• احرص على استقامة المستند</li>
                    <li>• تجنب الظلال والانعكاسات</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-3 text-red-600">❌ تجنب</h4>
                  <ul className="space-y-2 text-sm">
                    <li>• الصور الضبابية أو غير الواضحة</li>
                    <li>• الإضاءة الضعيفة أو القوية جداً</li>
                    <li>• قطع أجزاء من المستند</li>
                    <li>• الصور المائلة أو المقلوبة</li>
                    <li>• الملفات الكبيرة جداً (أكثر من 10MB)</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          <Alert>
            <FileText className="h-4 w-4" />
            <AlertDescription>
              <strong>نصيحة:</strong> للحصول على أفضل النتائج، تأكد من أن جميع النصوص 
              في المستند واضحة ومقروءة. إذا كان المستند يحتوي على عدة صفحات، 
              ارفع كل صفحة كملف منفصل.
            </AlertDescription>
          </Alert>
        </TabsContent>
      </Tabs>
    </div>
  );
}
