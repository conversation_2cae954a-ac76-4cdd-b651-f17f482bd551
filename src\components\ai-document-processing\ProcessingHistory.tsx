'use client';

import React, { useState, useEffect } from 'react';
import { 
  History, 
  Filter, 
  Download, 
  Eye, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  FileText,
  Calendar,
  TrendingUp
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

interface ProcessingHistoryProps {
  userType: 'merchant' | 'representative';
}

interface ProcessingRecord {
  id: string;
  status: 'processing' | 'completed' | 'failed' | 'requires_reupload';
  currentStage: string;
  userType: string;
  originalFileName: string;
  fileSize: number;
  mimeType: string;
  overallConfidence: number;
  
  results?: {
    decision: 'approve' | 'reject' | 'manual_review';
    documentType: string;
    riskScore: number;
  };

  qualityCheck: {
    imageQuality: number;
    blurDetected: boolean;
    resolutionSufficient: boolean;
    formatSupported: boolean;
  };

  errorsCount: number;
  warningsCount: number;
  createdAt: any;
  updatedAt: any;
  completedAt?: any;

  processingStats: {
    ocrTime: number;
    nerTime: number;
    classificationTime: number;
    totalTime: number;
  };
}

interface HistoryData {
  documents: ProcessingRecord[];
  stats: {
    total: number;
    completed: number;
    processing: number;
    failed: number;
    requiresReupload: number;
    averageConfidence: number;
    averageProcessingTime: number;
  };
  pagination: {
    hasMore: boolean;
    lastDocId: string | null;
  };
}

export default function ProcessingHistory({ userType }: ProcessingHistoryProps) {
  const [data, setData] = useState<HistoryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: '',
    search: '',
    dateRange: ''
  });

  const fetchHistory = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams();
      if (filters.status) params.append('status', filters.status);
      if (userType) params.append('userType', userType);
      
      const response = await fetch(`/api/ai-document-processing/history?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('فشل في جلب التاريخ');
      }

      const result = await response.json();
      setData(result.data);
    } catch (error) {
      console.error('خطأ في جلب التاريخ:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHistory();
  }, [filters.status, userType]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'processing':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'requires_reupload':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'مكتمل';
      case 'failed':
        return 'فشل';
      case 'processing':
        return 'جاري المعالجة';
      case 'requires_reupload':
        return 'يتطلب إعادة رفع';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'requires_reupload':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDecisionIcon = (decision: string) => {
    switch (decision) {
      case 'approve':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'reject':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'manual_review':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  const formatFileSize = (bytes: number) => {
    return (bytes / 1024 / 1024).toFixed(2) + ' MB';
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return '-';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA');
  };

  const formatProcessingTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center gap-2">
          <div className="h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
          <span>جاري تحميل التاريخ...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* العنوان والإحصائيات */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold flex items-center gap-2">
          <History className="h-6 w-6" />
          تاريخ معالجة المستندات
        </h1>
      </div>

      {/* الإحصائيات */}
      {data?.stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{data.stats.total}</p>
                <p className="text-sm text-gray-600">إجمالي المستندات</p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">{data.stats.completed}</p>
                <p className="text-sm text-gray-600">مكتملة</p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{data.stats.processing}</p>
                <p className="text-sm text-gray-600">قيد المعالجة</p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-red-600">{data.stats.failed}</p>
                <p className="text-sm text-gray-600">فشلت</p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">
                  {Math.round(data.stats.averageConfidence * 100)}%
                </p>
                <p className="text-sm text-gray-600">متوسط الثقة</p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-orange-600">
                  {formatProcessingTime(data.stats.averageProcessingTime)}
                </p>
                <p className="text-sm text-gray-600">متوسط الوقت</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* الفلاتر */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            الفلاتر والبحث
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">البحث</label>
              <Input
                placeholder="اسم الملف..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">الحالة</label>
              <Select
                value={filters.status}
                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="جميع الحالات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">جميع الحالات</SelectItem>
                  <SelectItem value="completed">مكتمل</SelectItem>
                  <SelectItem value="processing">قيد المعالجة</SelectItem>
                  <SelectItem value="failed">فشل</SelectItem>
                  <SelectItem value="requires_reupload">يتطلب إعادة رفع</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">التاريخ</label>
              <Select
                value={filters.dateRange}
                onValueChange={(value) => setFilters(prev => ({ ...prev, dateRange: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="جميع التواريخ" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">جميع التواريخ</SelectItem>
                  <SelectItem value="today">اليوم</SelectItem>
                  <SelectItem value="week">هذا الأسبوع</SelectItem>
                  <SelectItem value="month">هذا الشهر</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول التاريخ */}
      <Card>
        <CardHeader>
          <CardTitle>سجل المعالجات</CardTitle>
        </CardHeader>
        <CardContent>
          {data?.documents && data.documents.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>الملف</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>النتيجة</TableHead>
                    <TableHead>الثقة</TableHead>
                    <TableHead>الوقت</TableHead>
                    <TableHead>التاريخ</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.documents.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium truncate max-w-32">
                            {record.originalFileName}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatFileSize(record.fileSize)}
                          </p>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <Badge className={getStatusColor(record.status)}>
                          <span className="flex items-center gap-1">
                            {getStatusIcon(record.status)}
                            {getStatusText(record.status)}
                          </span>
                        </Badge>
                      </TableCell>
                      
                      <TableCell>
                        {record.results ? (
                          <div className="flex items-center gap-1">
                            {getDecisionIcon(record.results.decision)}
                            <span className="text-sm">
                              {record.results.decision === 'approve' ? 'موافق' :
                               record.results.decision === 'reject' ? 'مرفوض' : 'مراجعة'}
                            </span>
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="w-12 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ width: `${record.overallConfidence * 100}%` }}
                            />
                          </div>
                          <span className="text-sm">
                            {Math.round(record.overallConfidence * 100)}%
                          </span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <span className="text-sm">
                          {formatProcessingTime(record.processingStats.totalTime)}
                        </span>
                      </TableCell>
                      
                      <TableCell>
                        <span className="text-sm">
                          {formatDate(record.createdAt)}
                        </span>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600">لا توجد مستندات معالجة بعد</p>
              <p className="text-sm text-gray-500">ابدأ برفع مستندك الأول</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
