{"testDocuments": {"identity": {"valid": {"nationalId": "*********0", "name": "<PERSON>حمد محمد علي", "birthDate": "15/03/1990", "issueDate": "01/01/2020", "expiryDate": "01/01/2030", "expectedConfidence": 0.85, "expectedDecision": "approved"}, "incomplete": {"nationalId": "*********0", "name": "سارة أحمد", "expectedConfidence": 0.65, "expectedDecision": "requires_review"}, "invalid": {"nationalId": "123", "name": "نص غير واضح", "expectedConfidence": 0.3, "expectedDecision": "rejected"}}, "business": {"valid": {"registrationNumber": "**********", "businessName": "شركة التجارة المحدودة", "ownerName": "<PERSON><PERSON><PERSON><PERSON> أحمد", "issueDate": "01/06/2023", "expiryDate": "01/06/2024", "expectedConfidence": 0.8, "expectedDecision": "approved"}, "incomplete": {"registrationNumber": "**********", "businessName": "شركة ناقصة", "expectedConfidence": 0.6, "expectedDecision": "requires_review"}}, "financial": {"valid": {"accountNumber": "*********", "customerName": "سارة محمد", "balance": "15,000", "statementDate": "01/12/2023", "expectedConfidence": 0.75, "expectedDecision": "approved"}}}, "modelConfigurations": {"ocr": {"trocr_printed": {"id": "Xenova/trocr-base-printed", "name": "TrOCR للنصوص المطبوعة", "size": "~45MB", "expectedLoadTime": 30000, "priority": 1}, "trocr_handwritten": {"id": "Xenova/trocr-base-handwritten", "name": "TrOCR للخط اليدوي", "size": "~45MB", "expectedLoadTime": 30000, "priority": 2}}, "ner": {"bert_multilingual": {"id": "Xenova/bert-base-multilingual-cased", "name": "BERT متعدد اللغات", "size": "~110MB", "expectedLoadTime": 45000, "priority": 1}, "arabert_mini": {"id": "Xenova/arabert-base", "name": "AraBERT مضغوط", "size": "~85MB", "expectedLoadTime": 35000, "priority": 2}}, "classification": {"distilbert": {"id": "Xenova/distilbert-base-uncased-finetuned-sst-2-english", "name": "مصن<PERSON> المستندات", "size": "~65MB", "expectedLoadTime": 25000, "priority": 1}}}, "performanceBenchmarks": {"processing": {"maxProcessingTime": 30000, "targetProcessingTime": 15000, "maxMemoryUsage": 512, "targetMemoryUsage": 256}, "modelLoading": {"maxLoadTime": 120000, "targetLoadTime": 60000, "maxConcurrentModels": 5, "targetConcurrentModels": 3}, "optimization": {"minSuccessRate": 80, "targetSuccessRate": 95, "maxOptimizationTime": 10000, "targetOptimizationTime": 5000}}, "testScenarios": {"singleDocument": {"description": "معالجة مستند واحد", "steps": ["upload_document", "wait_for_processing", "verify_results"], "expectedDuration": 30000}, "multipleDocuments": {"description": "معالجة مستندات متعددة", "documentCount": 3, "steps": ["upload_documents_sequentially", "monitor_performance", "verify_all_results"], "expectedDuration": 90000}, "modelPreloading": {"description": "تحميل النماذج مسبقاً", "steps": ["preload_essential_models", "verify_model_status", "test_processing_speed"], "expectedDuration": 120000}, "memoryStress": {"description": "اختبار ضغط الذاكرة", "steps": ["load_all_models", "process_multiple_documents", "monitor_memory_usage", "verify_cleanup"], "expectedDuration": 180000}, "optimizationCycle": {"description": "دورة التحسين الكاملة", "steps": ["create_memory_pressure", "trigger_optimization", "verify_memory_freed", "test_performance_improvement"], "expectedDuration": 60000}}, "errorScenarios": {"corruptedFile": {"description": "ملف تالف", "fileContent": "corrupted_data", "expectedError": "فشل في معالجة المستند", "expectedRecovery": "fallback_processing"}, "unsupportedFormat": {"description": "تنسيق غير مدعوم", "fileExtension": ".txt", "expectedError": "تنسيق الملف غير مدعوم", "expectedRecovery": "format_conversion"}, "memoryExhaustion": {"description": "نفاد الذاكرة", "triggerCondition": "load_excessive_models", "expectedError": "الذاكرة المتاحة غير كافية", "expectedRecovery": "automatic_cleanup"}, "modelLoadFailure": {"description": "فشل تحميل النموذج", "triggerCondition": "network_interruption", "expectedError": "فشل في تحميل النموذج", "expectedRecovery": "fallback_model"}}, "securityTests": {"dataPrivacy": {"description": "حماية خصوصية البيانات", "checks": ["no_external_api_calls", "local_processing_only", "no_data_transmission"]}, "modelIntegrity": {"description": "سلامة النماذج", "checks": ["model_signature_verification", "checksum_validation", "source_verification"]}, "memoryIsolation": {"description": "عزل الذاكرة", "checks": ["no_memory_leaks", "proper_cleanup", "isolated_processing"]}}, "userTypes": {"merchant": {"requirements": {"identity": {"required_fields": ["nationalId", "name", "birthDate"], "min_confidence": 0.8, "auto_approve_threshold": 0.9}, "business": {"required_fields": ["registrationNumber", "businessName", "ownerName"], "min_confidence": 0.75, "auto_approve_threshold": 0.85}}}, "representative": {"requirements": {"identity": {"required_fields": ["nationalId", "name"], "min_confidence": 0.7, "auto_approve_threshold": 0.8}, "business": {"required_fields": ["registrationNumber", "businessName"], "min_confidence": 0.7, "auto_approve_threshold": 0.8}}}}, "apiEndpoints": {"upload": "/api/ai-document-processing/upload", "status": "/api/ai-document-processing/status", "models": "/api/ai-document-processing/models-status", "optimization": "/api/ai-document-processing/optimization"}, "expectedResponses": {"upload_success": {"success": true, "message": "تم رفع المستند وبدء المعالجة المحلية بنجاح", "data": {"processingType": "local", "benefits": ["معالجة مجانية", "حماية الخصوصية", "سرعة عالية"]}}, "models_status": {"success": true, "data": {"models": "array", "systemStats": "object", "capabilities": "object"}}, "processing_complete": {"status": "completed", "currentStage": "completed", "overallConfidence": "number", "ocrResult": "object", "nerResult": "object", "classificationResult": "object"}}}