// src/app/api/ai-document-processing/status/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من المصادقة
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'غير مصرح بالوصول' },
        { status: 401 }
      );
    }

    const token = authHeader.split('Bearer ')[1];
    let decodedToken;
    
    try {
      decodedToken = await getAuth().verifyIdToken(token);
    } catch (error) {
      return NextResponse.json(
        { error: 'رمز المصادقة غير صالح' },
        { status: 401 }
      );
    }

    const processingId = params.id;
    
    if (!processingId) {
      return NextResponse.json(
        { error: 'معرف المعالجة مطلوب' },
        { status: 400 }
      );
    }

    // جلب سجل المعالجة
    const docRef = doc(db, 'ai_document_processing', processingId);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      return NextResponse.json(
        { error: 'سجل المعالجة غير موجود' },
        { status: 404 }
      );
    }

    const processingData = docSnap.data();

    // التحقق من ملكية السجل
    if (processingData.userId !== decodedToken.uid) {
      return NextResponse.json(
        { error: 'غير مصرح بالوصول لهذا السجل' },
        { status: 403 }
      );
    }

    // تحضير البيانات للإرسال
    const response = {
      id: processingId,
      status: processingData.status,
      currentStage: processingData.currentStage,
      overallConfidence: processingData.overallConfidence || 0,
      
      // معلومات المراحل
      stages: {
        ocr: {
          completed: !!processingData.ocrResult,
          confidence: processingData.ocrResult?.confidence || 0,
          processingTime: processingData.ocrResult?.processingTime || 0,
          modelUsed: processingData.ocrResult?.modelUsed || ''
        },
        ner: {
          completed: !!processingData.nerResult,
          confidence: processingData.nerResult?.confidence || 0,
          processingTime: processingData.nerResult?.processingTime || 0,
          modelUsed: processingData.nerResult?.modelUsed || ''
        },
        classification: {
          completed: !!processingData.classificationResult,
          confidence: processingData.classificationResult?.confidence || 0,
          processingTime: processingData.classificationResult?.processingTime || 0,
          modelUsed: processingData.classificationResult?.modelUsed || ''
        }
      },

      // النتائج (إذا اكتملت)
      results: processingData.status === 'completed' ? {
        decision: processingData.classificationResult?.decision,
        documentType: processingData.classificationResult?.documentType,
        extractedData: processingData.nerResult?.extractedData,
        reasons: processingData.classificationResult?.reasons,
        riskScore: processingData.classificationResult?.riskScore
      } : null,

      // فحص الجودة
      qualityCheck: processingData.qualityCheck,

      // الأخطاء والتحذيرات
      errors: processingData.errors || [],
      warnings: processingData.warnings || [],

      // التوقيتات
      createdAt: processingData.createdAt,
      updatedAt: processingData.updatedAt,
      completedAt: processingData.completedAt || null,

      // معلومات الملف
      fileInfo: {
        originalFileName: processingData.originalFileName,
        fileSize: processingData.fileSize,
        mimeType: processingData.mimeType
      }
    };

    return NextResponse.json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error('خطأ في API حالة المعالجة:', error);
    
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 });
  }
}

// دعم OPTIONS للـ CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
