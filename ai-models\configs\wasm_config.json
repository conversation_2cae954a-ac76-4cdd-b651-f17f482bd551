{"version": "1.0.0", "description": "WebAssembly configuration for local processing", "wasm_modules": {"tesseract": {"core": "/ai-models/wasm/tesseract-core.wasm.js", "worker": "/ai-models/wasm/tesseract-worker.min.js", "languages": {"ara": "/ai-models/models/ocr/ara.traineddata", "eng": "/ai-models/models/ocr/eng.traineddata"}}, "onnx": {"runtime": "/ai-models/wasm/ort-wasm.wasm", "models": {"text_classifier": "/ai-models/models/nlp/text_classifier.onnx", "document_validator": "/ai-models/models/validation/validator.onnx"}}}, "performance": {"max_concurrent_workers": 2, "memory_limit": "256MB", "timeout": 30000}, "privacy": {"local_processing_only": true, "no_external_requests": true, "data_retention": "none"}}