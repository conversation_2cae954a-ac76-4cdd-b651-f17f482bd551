// src/services/advancedModelManager.ts - نظام إدارة النماذج المحلية المتقدم

import { pipeline, Pipeline, env } from '@xenova/transformers';
import { 
  ModelConfig, 
  TRANSFORMERS_CONFIG, 
  ModelTask, 
  SupportedLanguage 
} from '../config/transformersConfig';

// تكوين البيئة المحسن
env.allowRemoteModels = true;
env.allowLocalModels = true;
env.useBrowserCache = true;
env.backends.onnx.wasm.numThreads = navigator.hardwareConcurrency || 4;

interface LoadedModel {
  pipeline: Pipeline;
  config: ModelConfig;
  loadedAt: number;
  lastUsed: number;
  memoryUsage: number;
  usageCount: number;
  priority: number;
  isPreloaded: boolean;
}

interface ModelLoadProgress {
  modelId: string;
  progress: number;
  status: 'downloading' | 'loading' | 'ready' | 'error';
  stage: string;
  error?: string;
  estimatedTimeRemaining?: number;
}

interface MemoryStats {
  totalUsage: number;
  maxUsage: number;
  loadedModels: number;
  availableMemory: number;
  fragmentedMemory: number;
  cacheSize: number;
}

interface ModelManagerConfig {
  maxMemoryUsage: number;
  maxConcurrentModels: number;
  modelLoadTimeout: number;
  enableCaching: boolean;
  cacheSize: number;
  enableCompression: boolean;
  useWebWorkers: boolean;
  autoCleanup: boolean;
  cleanupInterval: number;
  preloadStrategy: 'aggressive' | 'conservative' | 'on-demand';
}

/**
 * نظام إدارة النماذج المحلية المتقدم
 * يوفر إدارة ذكية للذاكرة والأداء مع تحسينات متقدمة
 */
export class AdvancedModelManager {
  private static instance: AdvancedModelManager;
  private loadedModels: Map<string, LoadedModel> = new Map();
  private loadingPromises: Map<string, Promise<Pipeline>> = new Map();
  private progressCallbacks: Map<string, (progress: ModelLoadProgress) => void> = new Map();
  private memoryUsage: number = 0;
  private config: ModelManagerConfig;
  private cleanupTimer?: NodeJS.Timeout;
  private performanceMonitor: PerformanceMonitor;
  private modelCache: ModelCache;

  private constructor() {
    this.config = {
      maxMemoryUsage: TRANSFORMERS_CONFIG.performance.maxMemoryUsage * 1024 * 1024,
      maxConcurrentModels: TRANSFORMERS_CONFIG.performance.maxConcurrentModels,
      modelLoadTimeout: TRANSFORMERS_CONFIG.performance.modelLoadTimeout * 1000,
      enableCaching: TRANSFORMERS_CONFIG.performance.enableCaching,
      cacheSize: TRANSFORMERS_CONFIG.performance.cacheSize * 1024 * 1024,
      enableCompression: TRANSFORMERS_CONFIG.performance.enableCompression,
      useWebWorkers: TRANSFORMERS_CONFIG.performance.useWebWorkers,
      autoCleanup: true,
      cleanupInterval: 60000, // دقيقة واحدة
      preloadStrategy: 'conservative'
    };

    this.performanceMonitor = new PerformanceMonitor();
    this.modelCache = new ModelCache(this.config.cacheSize);
    this.setupAutoCleanup();
    this.setupPerformanceMonitoring();
  }

  static getInstance(): AdvancedModelManager {
    if (!AdvancedModelManager.instance) {
      AdvancedModelManager.instance = new AdvancedModelManager();
    }
    return AdvancedModelManager.instance;
  }

  /**
   * تحميل نموذج مع إدارة ذكية للذاكرة
   */
  async loadModel(
    modelId: string, 
    task: ModelTask,
    options: {
      priority?: number;
      preload?: boolean;
      onProgress?: (progress: ModelLoadProgress) => void;
      timeout?: number;
    } = {}
  ): Promise<Pipeline> {
    const startTime = Date.now();
    
    // التحقق من وجود النموذج محملاً مسبقاً
    const existingModel = this.loadedModels.get(modelId);
    if (existingModel) {
      existingModel.lastUsed = Date.now();
      existingModel.usageCount++;
      this.performanceMonitor.recordModelUsage(modelId, Date.now() - startTime);
      return existingModel.pipeline;
    }

    // التحقق من وجود عملية تحميل جارية
    const existingPromise = this.loadingPromises.get(modelId);
    if (existingPromise) {
      return existingPromise;
    }

    // تسجيل callback للتقدم
    if (options.onProgress) {
      this.progressCallbacks.set(modelId, options.onProgress);
    }

    // بدء تحميل النموذج
    const loadPromise = this.performModelLoad(modelId, task, options);
    this.loadingPromises.set(modelId, loadPromise);

    try {
      const pipeline = await loadPromise;
      this.loadingPromises.delete(modelId);
      this.progressCallbacks.delete(modelId);
      
      const loadTime = Date.now() - startTime;
      this.performanceMonitor.recordModelLoad(modelId, loadTime, true);
      
      return pipeline;
    } catch (error) {
      this.loadingPromises.delete(modelId);
      this.progressCallbacks.delete(modelId);
      
      const loadTime = Date.now() - startTime;
      this.performanceMonitor.recordModelLoad(modelId, loadTime, false);
      
      throw error;
    }
  }

  /**
   * تنفيذ تحميل النموذج مع تحسينات متقدمة
   */
  private async performModelLoad(
    modelId: string, 
    task: ModelTask, 
    options: any
  ): Promise<Pipeline> {
    const config = this.getModelConfig(modelId, task);
    if (!config) {
      throw new Error(`تكوين النموذج غير موجود: ${modelId}`);
    }

    this.reportProgress(modelId, 0, 'downloading', 'بدء التحميل...');

    try {
      // التحقق من الذاكرة المتاحة وتحسينها
      await this.optimizeMemoryForModel(config);

      // التحقق من الكاش أولاً
      let pipelineInstance = await this.modelCache.get(modelId);
      
      if (!pipelineInstance) {
        this.reportProgress(modelId, 10, 'downloading', 'تحميل النموذج...');
        
        // تحميل النموذج مع timeout
        const timeout = options.timeout || this.config.modelLoadTimeout;
        pipelineInstance = await Promise.race([
          this.loadModelWithProgress(modelId, task, config),
          this.createTimeoutPromise(timeout, `انتهت مهلة تحميل النموذج ${modelId}`)
        ]);

        // حفظ في الكاش
        await this.modelCache.set(modelId, pipelineInstance);
      }

      // حفظ النموذج المحمل
      const loadedModel: LoadedModel = {
        pipeline: pipelineInstance,
        config,
        loadedAt: Date.now(),
        lastUsed: Date.now(),
        memoryUsage: this.estimateModelMemory(config),
        usageCount: 1,
        priority: options.priority || config.priority,
        isPreloaded: options.preload || false
      };

      this.loadedModels.set(modelId, loadedModel);
      this.memoryUsage += loadedModel.memoryUsage;

      this.reportProgress(modelId, 100, 'ready', 'النموذج جاهز للاستخدام');
      console.log(`✅ تم تحميل النموذج بنجاح: ${config.name} (${config.size})`);

      return pipelineInstance;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      this.reportProgress(modelId, 0, 'error', 'فشل في التحميل', errorMessage);
      console.error(`❌ فشل في تحميل النموذج ${modelId}:`, error);
      throw new Error(`فشل في تحميل النموذج: ${errorMessage}`);
    }
  }

  /**
   * تحميل النموذج مع تتبع التقدم
   */
  private async loadModelWithProgress(
    modelId: string, 
    task: ModelTask, 
    config: ModelConfig
  ): Promise<Pipeline> {
    return pipeline(
      this.getTaskType(task),
      config.id,
      {
        progress_callback: (progress: any) => {
          if (progress.loaded && progress.total) {
            const percentage = Math.round((progress.loaded / progress.total) * 90) + 10; // 10-100%
            const stage = progress.status === 'download' ? 'تحميل الملفات...' : 'تحضير النموذج...';
            const estimatedTime = this.estimateRemainingTime(progress);
            
            this.reportProgress(modelId, percentage, 'loading', stage, undefined, estimatedTime);
          }
        }
      }
    );
  }

  /**
   * تحسين الذاكرة قبل تحميل نموذج جديد
   */
  private async optimizeMemoryForModel(config: ModelConfig): Promise<void> {
    const requiredMemory = this.estimateModelMemory(config);
    const availableMemory = this.config.maxMemoryUsage - this.memoryUsage;

    if (requiredMemory > availableMemory) {
      console.log(`🧹 تحسين الذاكرة: مطلوب ${this.formatBytes(requiredMemory)}, متاح ${this.formatBytes(availableMemory)}`);
      
      // تحرير النماذج الأقل استخداماً
      await this.smartMemoryCleanup(requiredMemory);
      
      // إجراء garbage collection إذا كان متاحاً
      if (typeof window !== 'undefined' && 'gc' in window) {
        (window as any).gc();
      }
      
      // التحقق مرة أخرى
      const newAvailableMemory = this.config.maxMemoryUsage - this.memoryUsage;
      if (requiredMemory > newAvailableMemory) {
        throw new Error(`الذاكرة المتاحة غير كافية: مطلوب ${this.formatBytes(requiredMemory)}, متاح ${this.formatBytes(newAvailableMemory)}`);
      }
    }
  }

  /**
   * تنظيف ذكي للذاكرة
   */
  private async smartMemoryCleanup(requiredMemory: number): Promise<void> {
    const modelsToRemove: string[] = [];
    let freedMemory = 0;

    // ترتيب النماذج حسب الأولوية للإزالة
    const sortedModels = Array.from(this.loadedModels.entries())
      .sort(([, a], [, b]) => {
        // النماذج المحملة مسبقاً لها أولوية أقل للإزالة
        if (a.isPreloaded !== b.isPreloaded) {
          return a.isPreloaded ? 1 : -1;
        }
        
        // النماذج الأقل استخداماً أولى بالإزالة
        const aScore = a.usageCount / (Date.now() - a.lastUsed);
        const bScore = b.usageCount / (Date.now() - b.lastUsed);
        return aScore - bScore;
      });

    for (const [modelId, model] of sortedModels) {
      if (freedMemory >= requiredMemory) break;
      
      // لا نزيل النماذج المستخدمة حديثاً (آخر 5 دقائق)
      const timeSinceLastUse = Date.now() - model.lastUsed;
      if (timeSinceLastUse < 5 * 60 * 1000) continue;
      
      modelsToRemove.push(modelId);
      freedMemory += model.memoryUsage;
    }

    // إزالة النماذج المحددة
    for (const modelId of modelsToRemove) {
      await this.unloadModel(modelId);
    }

    console.log(`🧹 تم تحرير ${this.formatBytes(freedMemory)} من الذاكرة`);
  }

  /**
   * تحميل مسبق للنماذج الأساسية
   */
  async preloadEssentialModels(
    onProgress?: (overall: number, current: string) => void
  ): Promise<void> {
    const essentialModels = [
      { id: 'Xenova/trocr-base-printed', task: 'ocr' as ModelTask },
      { id: 'Xenova/bert-base-multilingual-cased', task: 'ner' as ModelTask },
      { id: 'Xenova/distilbert-base-uncased-finetuned-sst-2-english', task: 'classification' as ModelTask }
    ];

    console.log('🚀 بدء التحميل المسبق للنماذج الأساسية...');

    for (let i = 0; i < essentialModels.length; i++) {
      const { id, task } = essentialModels[i];
      const overallProgress = ((i / essentialModels.length) * 100);
      
      onProgress?.(overallProgress, id);
      
      try {
        await this.loadModel(id, task, { 
          preload: true,
          priority: 1,
          onProgress: (progress) => {
            const adjustedProgress = overallProgress + (progress.progress / essentialModels.length);
            onProgress?.(adjustedProgress, `${id}: ${progress.stage}`);
          }
        });
      } catch (error) {
        console.warn(`⚠️ فشل في التحميل المسبق للنموذج ${id}:`, error);
      }
    }

    onProgress?.(100, 'اكتمل التحميل المسبق');
    console.log('✅ اكتمل التحميل المسبق للنماذج الأساسية');
  }

  /**
   * إلغاء تحميل نموذج محدد
   */
  async unloadModel(modelId: string): Promise<boolean> {
    const model = this.loadedModels.get(modelId);
    if (!model) return false;

    try {
      // تحرير موارد النموذج
      if (model.pipeline && typeof model.pipeline.dispose === 'function') {
        await model.pipeline.dispose();
      }

      this.memoryUsage -= model.memoryUsage;
      this.loadedModels.delete(modelId);
      
      console.log(`🗑️ تم إلغاء تحميل النموذج: ${model.config.name}`);
      return true;
    } catch (error) {
      console.error(`❌ خطأ في إلغاء تحميل النموذج ${modelId}:`, error);
      return false;
    }
  }

  /**
   * الحصول على إحصائيات مفصلة للذاكرة
   */
  getDetailedMemoryStats(): MemoryStats & {
    modelBreakdown: Array<{
      id: string;
      name: string;
      memoryUsage: number;
      usageCount: number;
      lastUsed: number;
      priority: number;
    }>;
    recommendations: string[];
  } {
    const baseStats = this.getMemoryStats();
    
    const modelBreakdown = Array.from(this.loadedModels.entries()).map(([id, model]) => ({
      id,
      name: model.config.name,
      memoryUsage: model.memoryUsage,
      usageCount: model.usageCount,
      lastUsed: model.lastUsed,
      priority: model.priority
    }));

    const recommendations = this.generateMemoryRecommendations();

    return {
      ...baseStats,
      fragmentedMemory: this.estimateFragmentedMemory(),
      cacheSize: this.modelCache.getSize(),
      modelBreakdown,
      recommendations
    };
  }

  // الطرق المساعدة الخاصة

  private getMemoryStats(): MemoryStats {
    return {
      totalUsage: this.memoryUsage,
      maxUsage: this.config.maxMemoryUsage,
      loadedModels: this.loadedModels.size,
      availableMemory: this.config.maxMemoryUsage - this.memoryUsage,
      fragmentedMemory: 0,
      cacheSize: 0
    };
  }

  private getModelConfig(modelId: string, task: ModelTask): ModelConfig | null {
    const models = TRANSFORMERS_CONFIG.models[task];
    return Object.values(models).find(model => model.id === modelId) || null;
  }

  private getTaskType(task: ModelTask): string {
    const taskMap = {
      'ocr': 'image-to-text',
      'ner': 'token-classification',
      'classification': 'text-classification'
    };
    return taskMap[task];
  }

  private estimateModelMemory(config: ModelConfig): number {
    const sizeStr = config.size.replace(/[^\d]/g, '');
    const sizeNum = parseInt(sizeStr) || 50;
    return sizeNum * 1024 * 1024;
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private reportProgress(
    modelId: string, 
    progress: number, 
    status: ModelLoadProgress['status'], 
    stage: string,
    error?: string,
    estimatedTimeRemaining?: number
  ): void {
    const callback = this.progressCallbacks.get(modelId);
    if (callback) {
      callback({ modelId, progress, status, stage, error, estimatedTimeRemaining });
    }
  }

  private estimateRemainingTime(progress: any): number {
    if (!progress.loaded || !progress.total) return 0;
    
    const remainingBytes = progress.total - progress.loaded;
    const avgSpeed = progress.loaded / (Date.now() - (progress.startTime || Date.now()));
    return remainingBytes / avgSpeed;
  }

  private createTimeoutPromise<T>(timeout: number, message: string): Promise<T> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error(message)), timeout);
    });
  }

  private estimateFragmentedMemory(): number {
    // تقدير تقريبي للذاكرة المجزأة
    return this.memoryUsage * 0.1; // 10% تقدير
  }

  private generateMemoryRecommendations(): string[] {
    const recommendations: string[] = [];
    const stats = this.getMemoryStats();
    const usagePercentage = (stats.totalUsage / stats.maxUsage) * 100;

    if (usagePercentage > 80) {
      recommendations.push('الذاكرة ممتلئة تقريباً - فكر في إلغاء تحميل النماذج غير المستخدمة');
    }

    if (this.loadedModels.size > this.config.maxConcurrentModels) {
      recommendations.push('عدد النماذج المحملة يتجاوز الحد الأقصى المنصوح به');
    }

    const oldModels = Array.from(this.loadedModels.values())
      .filter(model => Date.now() - model.lastUsed > 10 * 60 * 1000);
    
    if (oldModels.length > 0) {
      recommendations.push(`يوجد ${oldModels.length} نموذج لم يستخدم لأكثر من 10 دقائق`);
    }

    if (recommendations.length === 0) {
      recommendations.push('استخدام الذاكرة مثالي');
    }

    return recommendations;
  }

  private setupAutoCleanup(): void {
    if (this.config.autoCleanup) {
      this.cleanupTimer = setInterval(() => {
        this.performAutoCleanup();
      }, this.config.cleanupInterval);
    }
  }

  private setupPerformanceMonitoring(): void {
    // مراقبة الأداء كل 30 ثانية
    setInterval(() => {
      this.performanceMonitor.generateReport();
    }, 30000);
  }

  private async performAutoCleanup(): Promise<void> {
    const currentTime = Date.now();
    const maxIdleTime = 15 * 60 * 1000; // 15 دقيقة

    for (const [modelId, model] of this.loadedModels.entries()) {
      const isIdle = currentTime - model.lastUsed > maxIdleTime;
      const isLowPriority = model.priority > 2;
      const isNotPreloaded = !model.isPreloaded;

      if (isIdle && isLowPriority && isNotPreloaded) {
        await this.unloadModel(modelId);
      }
    }
  }
}

/**
 * مراقب الأداء المتقدم للنماذج
 */
class PerformanceMonitor {
  private loadTimes: Map<string, number[]> = new Map();
  private usageTimes: Map<string, number[]> = new Map();
  private loadSuccessRates: Map<string, { success: number; total: number }> = new Map();
  private memoryUsageHistory: Array<{ timestamp: number; usage: number }> = [];
  private errorLogs: Array<{ timestamp: number; modelId: string; error: string }> = [];
  private performanceMetrics: Map<string, {
    avgLoadTime: number;
    avgUsageTime: number;
    successRate: number;
    totalUsages: number;
    lastUsed: number;
  }> = new Map();

  recordModelLoad(modelId: string, loadTime: number, success: boolean): void {
    // تسجيل أوقات التحميل
    if (!this.loadTimes.has(modelId)) {
      this.loadTimes.set(modelId, []);
    }
    this.loadTimes.get(modelId)!.push(loadTime);

    // تسجيل معدلات النجاح
    if (!this.loadSuccessRates.has(modelId)) {
      this.loadSuccessRates.set(modelId, { success: 0, total: 0 });
    }
    const rates = this.loadSuccessRates.get(modelId)!;
    rates.total++;
    if (success) rates.success++;

    // تحديث المقاييس
    this.updatePerformanceMetrics(modelId);

    // تسجيل الأخطاء
    if (!success) {
      this.errorLogs.push({
        timestamp: Date.now(),
        modelId,
        error: 'فشل في تحميل النموذج'
      });
    }
  }

  recordModelUsage(modelId: string, usageTime: number): void {
    if (!this.usageTimes.has(modelId)) {
      this.usageTimes.set(modelId, []);
    }
    this.usageTimes.get(modelId)!.push(usageTime);

    // تحديث المقاييس
    this.updatePerformanceMetrics(modelId);
  }

  recordMemoryUsage(usage: number): void {
    this.memoryUsageHistory.push({
      timestamp: Date.now(),
      usage
    });

    // الاحتفاظ بآخر 100 قراءة فقط
    if (this.memoryUsageHistory.length > 100) {
      this.memoryUsageHistory.shift();
    }
  }

  recordError(modelId: string, error: string): void {
    this.errorLogs.push({
      timestamp: Date.now(),
      modelId,
      error
    });

    // الاحتفاظ بآخر 50 خطأ فقط
    if (this.errorLogs.length > 50) {
      this.errorLogs.shift();
    }
  }

  private updatePerformanceMetrics(modelId: string): void {
    const loadTimes = this.loadTimes.get(modelId) || [];
    const usageTimes = this.usageTimes.get(modelId) || [];
    const rates = this.loadSuccessRates.get(modelId) || { success: 0, total: 0 };

    const avgLoadTime = loadTimes.length > 0
      ? loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length
      : 0;

    const avgUsageTime = usageTimes.length > 0
      ? usageTimes.reduce((a, b) => a + b, 0) / usageTimes.length
      : 0;

    const successRate = rates.total > 0 ? (rates.success / rates.total) * 100 : 0;

    this.performanceMetrics.set(modelId, {
      avgLoadTime,
      avgUsageTime,
      successRate,
      totalUsages: usageTimes.length,
      lastUsed: Date.now()
    });
  }

  generateDetailedReport(): {
    modelMetrics: Map<string, any>;
    systemMetrics: {
      totalModelsLoaded: number;
      totalUsages: number;
      avgSystemLoadTime: number;
      systemSuccessRate: number;
      memoryTrend: 'increasing' | 'decreasing' | 'stable';
      recentErrors: Array<{ timestamp: number; modelId: string; error: string }>;
    };
    recommendations: string[];
  } {
    const systemMetrics = this.calculateSystemMetrics();
    const recommendations = this.generateRecommendations();

    return {
      modelMetrics: this.performanceMetrics,
      systemMetrics,
      recommendations
    };
  }

  private calculateSystemMetrics() {
    const allLoadTimes = Array.from(this.loadTimes.values()).flat();
    const allUsageTimes = Array.from(this.usageTimes.values()).flat();
    const allRates = Array.from(this.loadSuccessRates.values());

    const totalSuccess = allRates.reduce((sum, rate) => sum + rate.success, 0);
    const totalAttempts = allRates.reduce((sum, rate) => sum + rate.total, 0);

    // تحليل اتجاه استخدام الذاكرة
    let memoryTrend: 'increasing' | 'decreasing' | 'stable' = 'stable';
    if (this.memoryUsageHistory.length >= 10) {
      const recent = this.memoryUsageHistory.slice(-5);
      const older = this.memoryUsageHistory.slice(-10, -5);
      const recentAvg = recent.reduce((sum, item) => sum + item.usage, 0) / recent.length;
      const olderAvg = older.reduce((sum, item) => sum + item.usage, 0) / older.length;

      if (recentAvg > olderAvg * 1.1) memoryTrend = 'increasing';
      else if (recentAvg < olderAvg * 0.9) memoryTrend = 'decreasing';
    }

    return {
      totalModelsLoaded: this.performanceMetrics.size,
      totalUsages: allUsageTimes.length,
      avgSystemLoadTime: allLoadTimes.length > 0
        ? allLoadTimes.reduce((a, b) => a + b, 0) / allLoadTimes.length
        : 0,
      systemSuccessRate: totalAttempts > 0 ? (totalSuccess / totalAttempts) * 100 : 0,
      memoryTrend,
      recentErrors: this.errorLogs.slice(-10)
    };
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const systemMetrics = this.calculateSystemMetrics();

    // توصيات بناءً على معدل النجاح
    if (systemMetrics.systemSuccessRate < 90) {
      recommendations.push('معدل نجاح التحميل منخفض - تحقق من الاتصال بالإنترنت أو مساحة التخزين');
    }

    // توصيات بناءً على أوقات التحميل
    if (systemMetrics.avgSystemLoadTime > 30000) { // 30 ثانية
      recommendations.push('أوقات التحميل طويلة - فكر في تحسين الشبكة أو استخدام نماذج أصغر');
    }

    // توصيات بناءً على اتجاه الذاكرة
    if (systemMetrics.memoryTrend === 'increasing') {
      recommendations.push('استخدام الذاكرة في تزايد - فعل التنظيف التلقائي');
    }

    // توصيات بناءً على الأخطاء الأخيرة
    if (systemMetrics.recentErrors.length > 5) {
      recommendations.push('عدد كبير من الأخطاء الأخيرة - راجع سجل الأخطاء');
    }

    // توصيات عامة
    if (recommendations.length === 0) {
      recommendations.push('النظام يعمل بكفاءة عالية');
    }

    return recommendations;
  }

  generateReport(): void {
    const report = this.generateDetailedReport();

    console.log('📊 تقرير أداء النماذج المفصل:');
    console.log('🔧 مقاييس النظام:', report.systemMetrics);

    console.log('📈 مقاييس النماذج:');
    for (const [modelId, metrics] of report.modelMetrics.entries()) {
      console.log(`  ${modelId}:`, {
        'متوسط التحميل': `${metrics.avgLoadTime.toFixed(0)}ms`,
        'متوسط الاستخدام': `${metrics.avgUsageTime.toFixed(0)}ms`,
        'معدل النجاح': `${metrics.successRate.toFixed(1)}%`,
        'إجمالي الاستخدامات': metrics.totalUsages
      });
    }

    console.log('💡 التوصيات:', report.recommendations);
  }

  // تصدير البيانات للتحليل الخارجي
  exportData(): {
    loadTimes: Record<string, number[]>;
    usageTimes: Record<string, number[]>;
    successRates: Record<string, { success: number; total: number }>;
    memoryHistory: Array<{ timestamp: number; usage: number }>;
    errorLogs: Array<{ timestamp: number; modelId: string; error: string }>;
  } {
    return {
      loadTimes: Object.fromEntries(this.loadTimes),
      usageTimes: Object.fromEntries(this.usageTimes),
      successRates: Object.fromEntries(this.loadSuccessRates),
      memoryHistory: [...this.memoryUsageHistory],
      errorLogs: [...this.errorLogs]
    };
  }

  // مسح البيانات القديمة
  clearOldData(olderThanDays: number = 7): void {
    const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);

    // مسح سجل الذاكرة القديم
    this.memoryUsageHistory = this.memoryUsageHistory.filter(
      item => item.timestamp > cutoffTime
    );

    // مسح سجل الأخطاء القديم
    this.errorLogs = this.errorLogs.filter(
      item => item.timestamp > cutoffTime
    );

    console.log(`🧹 تم مسح البيانات الأقدم من ${olderThanDays} أيام`);
  }
}

/**
 * كاش النماذج المحلي
 */
class ModelCache {
  private cache: Map<string, Pipeline> = new Map();
  private maxSize: number;
  private currentSize: number = 0;

  constructor(maxSize: number) {
    this.maxSize = maxSize;
  }

  async get(modelId: string): Promise<Pipeline | null> {
    return this.cache.get(modelId) || null;
  }

  async set(modelId: string, pipeline: Pipeline): Promise<void> {
    // تنفيذ LRU cache بسيط
    if (this.currentSize >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
        this.currentSize--;
      }
    }

    this.cache.set(modelId, pipeline);
    this.currentSize++;
  }

  getSize(): number {
    return this.currentSize;
  }

  clear(): void {
    this.cache.clear();
    this.currentSize = 0;
  }
}

// تصدير المثيل الوحيد
export const advancedModelManager = AdvancedModelManager.getInstance();
