// src/app/api/ai-document-processing/history/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { collection, query, where, orderBy, limit, getDocs, startAfter } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export async function GET(request: NextRequest) {
  try {
    // التحقق من المصادقة
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'غير مصرح بالوصول' },
        { status: 401 }
      );
    }

    const token = authHeader.split('Bearer ')[1];
    let decodedToken;
    
    try {
      decodedToken = await getAuth().verifyIdToken(token);
    } catch (error) {
      return NextResponse.json(
        { error: 'رمز المصادقة غير صالح' },
        { status: 401 }
      );
    }

    // استخراج معاملات الاستعلام
    const { searchParams } = new URL(request.url);
    const pageSize = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');
    const userType = searchParams.get('userType');
    const lastDocId = searchParams.get('lastDocId');

    // بناء الاستعلام
    let q = query(
      collection(db, 'ai_document_processing'),
      where('userId', '==', decodedToken.uid),
      orderBy('createdAt', 'desc'),
      limit(pageSize)
    );

    // إضافة فلاتر إضافية
    if (status && ['processing', 'completed', 'failed', 'requires_reupload'].includes(status)) {
      q = query(q, where('status', '==', status));
    }

    if (userType && ['merchant', 'representative'].includes(userType)) {
      q = query(q, where('userType', '==', userType));
    }

    // التصفح (Pagination)
    if (lastDocId) {
      // في التطبيق الحقيقي، نحتاج للحصول على آخر مستند
      // q = query(q, startAfter(lastDoc));
    }

    // تنفيذ الاستعلام
    const querySnapshot = await getDocs(q);
    
    const documents = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        status: data.status,
        currentStage: data.currentStage,
        userType: data.userType,
        originalFileName: data.originalFileName,
        fileSize: data.fileSize,
        mimeType: data.mimeType,
        overallConfidence: data.overallConfidence || 0,
        
        // النتائج المختصرة
        results: data.status === 'completed' ? {
          decision: data.classificationResult?.decision,
          documentType: data.classificationResult?.documentType,
          riskScore: data.classificationResult?.riskScore
        } : null,

        // فحص الجودة
        qualityCheck: data.qualityCheck,

        // الأخطاء (فقط العدد)
        errorsCount: (data.errors || []).length,
        warningsCount: (data.warnings || []).length,

        // التوقيتات
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        completedAt: data.completedAt || null,

        // إحصائيات المعالجة
        processingStats: {
          ocrTime: data.ocrResult?.processingTime || 0,
          nerTime: data.nerResult?.processingTime || 0,
          classificationTime: data.classificationResult?.processingTime || 0,
          totalTime: (data.ocrResult?.processingTime || 0) + 
                    (data.nerResult?.processingTime || 0) + 
                    (data.classificationResult?.processingTime || 0)
        }
      };
    });

    // إحصائيات إضافية
    const stats = {
      total: documents.length,
      completed: documents.filter(d => d.status === 'completed').length,
      processing: documents.filter(d => d.status === 'processing').length,
      failed: documents.filter(d => d.status === 'failed').length,
      requiresReupload: documents.filter(d => d.status === 'requires_reupload').length,
      
      // متوسط الثقة
      averageConfidence: documents.length > 0 
        ? documents.reduce((sum, d) => sum + d.overallConfidence, 0) / documents.length 
        : 0,
      
      // متوسط وقت المعالجة
      averageProcessingTime: documents.length > 0
        ? documents.reduce((sum, d) => sum + d.processingStats.totalTime, 0) / documents.length
        : 0
    };

    return NextResponse.json({
      success: true,
      data: {
        documents,
        stats,
        pagination: {
          hasMore: querySnapshot.docs.length === pageSize,
          lastDocId: querySnapshot.docs.length > 0 
            ? querySnapshot.docs[querySnapshot.docs.length - 1].id 
            : null
        }
      }
    });

  } catch (error) {
    console.error('خطأ في API تاريخ المعالجة:', error);
    
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 });
  }
}

// دعم OPTIONS للـ CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
