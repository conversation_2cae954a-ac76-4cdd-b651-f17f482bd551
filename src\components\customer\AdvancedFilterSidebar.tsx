"use client";

import { useState, useEffect } from 'react';
import { useLocale } from '@/hooks/use-locale';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  Filter, 
  Star, 
  MapPin, 
  Clock, 
  Truck, 
  Shield, 
  Zap,
  Tag,
  TrendingUp,
  ChevronDown,
  ChevronUp,
  X,
  Rotate<PERSON>cw
} from 'lucide-react';
import type { SearchFilter } from '@/types';
import { cn } from '@/lib/utils';

interface AdvancedFilterSidebarProps {
  filters: SearchFilter;
  onFiltersChange: (filters: SearchFilter) => void;
  onClearFilters: () => void;
  isMobile?: boolean;
  className?: string;
  availableCategories?: Array<{ id: string; name: string; count: number }>;
  storeStats?: {
    totalStores: number;
    openStores: number;
    verifiedStores: number;
    averageRating: number;
  };
}

interface FilterSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  isOpen: boolean;
}

export default function AdvancedFilterSidebar({
  filters,
  onFiltersChange,
  onClearFilters,
  isMobile = false,
  className = "",
  availableCategories = [],
  storeStats
}: AdvancedFilterSidebarProps) {
  const { t } = useLocale();
  
  const [isOpen, setIsOpen] = useState(false);
  const [priceRange, setPriceRange] = useState<[number, number]>([
    filters.minPrice || 0,
    filters.maxPrice || 1000
  ]);
  const [distanceRange, setDistanceRange] = useState<number>(filters.maxDistance || 50);
  
  const [filterSections, setFilterSections] = useState<FilterSection[]>([
    { id: 'categories', title: 'الفئات', icon: <Tag className="w-4 h-4" />, isOpen: true },
    { id: 'price', title: 'السعر', icon: <Tag className="w-4 h-4" />, isOpen: true },
    { id: 'rating', title: 'التقييم', icon: <Star className="w-4 h-4" />, isOpen: true },
    { id: 'location', title: 'الموقع والمسافة', icon: <MapPin className="w-4 h-4" />, isOpen: false },
    { id: 'availability', title: 'التوفر والتسليم', icon: <Truck className="w-4 h-4" />, isOpen: false },
    { id: 'store', title: 'خصائص المتجر', icon: <Shield className="w-4 h-4" />, isOpen: false },
    { id: 'advanced', title: 'فلاتر متقدمة', icon: <Zap className="w-4 h-4" />, isOpen: false }
  ]);

  // تحديث نطاق السعر عند تغيير الفلاتر
  useEffect(() => {
    setPriceRange([filters.minPrice || 0, filters.maxPrice || 1000]);
  }, [filters.minPrice, filters.maxPrice]);

  // تحديث نطاق المسافة
  useEffect(() => {
    setDistanceRange(filters.maxDistance || 50);
  }, [filters.maxDistance]);

  const toggleSection = (sectionId: string) => {
    setFilterSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { ...section, isOpen: !section.isOpen }
          : section
      )
    );
  };

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    const currentCategories = filters.categories || [];
    const newCategories = checked
      ? [...currentCategories, categoryId]
      : currentCategories.filter(id => id !== categoryId);
    
    onFiltersChange({ ...filters, categories: newCategories });
  };

  const handlePriceRangeChange = (values: number[]) => {
    setPriceRange([values[0], values[1]]);
  };

  const handlePriceRangeCommit = (values: number[]) => {
    onFiltersChange({
      ...filters,
      minPrice: values[0],
      maxPrice: values[1]
    });
  };

  const handleRatingChange = (rating: number) => {
    onFiltersChange({
      ...filters,
      minRating: filters.minRating === rating ? undefined : rating
    });
  };

  const handleDistanceChange = (value: number[]) => {
    const distance = value[0];
    setDistanceRange(distance);
    onFiltersChange({ ...filters, maxDistance: distance });
  };

  const handleSwitchChange = (key: keyof SearchFilter, value: boolean) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const getActiveFiltersCount = (): number => {
    let count = 0;
    if (filters.categories?.length) count += filters.categories.length;
    if (filters.minPrice || filters.maxPrice) count++;
    if (filters.minRating) count++;
    if (filters.maxDistance && filters.maxDistance < 50) count++;
    if (filters.verifiedOnly) count++;
    if (filters.openNow) count++;
    if (filters.hasDelivery) count++;
    if (filters.fastDelivery) count++;
    if (filters.hasOffers) count++;
    if (filters.newArrivals) count++;
    if (filters.trending) count++;
    if (filters.featured) count++;
    return count;
  };

  const ratingOptions = [
    { value: 5, label: '5 نجوم فقط' },
    { value: 4, label: '4 نجوم فأكثر' },
    { value: 3, label: '3 نجوم فأكثر' },
    { value: 2, label: '2 نجوم فأكثر' },
    { value: 1, label: 'نجمة واحدة فأكثر' }
  ];

  const FilterContent = () => (
    <div className="space-y-6">
      {/* إحصائيات سريعة */}
      {storeStats && (
        <Card className="bg-muted/30">
          <CardContent className="p-4">
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div className="text-center">
                <div className="font-semibold text-lg">{storeStats.totalStores}</div>
                <div className="text-muted-foreground">إجمالي المتاجر</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-lg text-green-600">{storeStats.openStores}</div>
                <div className="text-muted-foreground">مفتوح الآن</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* أزرار المسح والإعادة */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onClearFilters}
          className="flex-1"
          disabled={getActiveFiltersCount() === 0}
        >
          <RotateCcw className="w-3 h-3 mr-1" />
          مسح الكل
        </Button>
        {getActiveFiltersCount() > 0 && (
          <Badge variant="secondary" className="px-2 py-1">
            {getActiveFiltersCount()} فلتر نشط
          </Badge>
        )}
      </div>

      {/* أقسام الفلاتر */}
      {filterSections.map((section) => (
        <Collapsible
          key={section.id}
          open={section.isOpen}
          onOpenChange={() => toggleSection(section.id)}
        >
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <div className="flex items-center gap-2">
                {section.icon}
                <span className="font-medium">{section.title}</span>
              </div>
              {section.isOpen ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          
          <CollapsibleContent className="mt-3 space-y-3">
            {section.id === 'categories' && (
              <div className="space-y-2">
                {availableCategories.map((category) => (
                  <div key={category.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Checkbox
                        id={`category-${category.id}`}
                        checked={filters.categories?.includes(category.id) || false}
                        onCheckedChange={(checked) => 
                          handleCategoryChange(category.id, checked as boolean)
                        }
                      />
                      <Label 
                        htmlFor={`category-${category.id}`}
                        className="text-sm cursor-pointer"
                      >
                        {category.name}
                      </Label>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {category.count}
                    </Badge>
                  </div>
                ))}
              </div>
            )}

            {section.id === 'price' && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="min-price-advanced" className="text-sm text-muted-foreground">
                      من
                    </Label>
                    <Input
                      id="min-price-advanced"
                      type="number"
                      placeholder="0"
                      value={priceRange[0]}
                      onChange={(e) => {
                        const value = Number(e.target.value) || 0;
                        handlePriceRangeChange([value, priceRange[1]]);
                      }}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="max-price-advanced" className="text-sm text-muted-foreground">
                      إلى
                    </Label>
                    <Input
                      id="max-price-advanced"
                      type="number"
                      placeholder="1000"
                      value={priceRange[1]}
                      onChange={(e) => {
                        const value = Number(e.target.value) || 1000;
                        handlePriceRangeChange([priceRange[0], value]);
                      }}
                      className="mt-1"
                    />
                  </div>
                </div>
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>{priceRange[0]} ريال</span>
                  <span>{priceRange[1]} ريال</span>
                </div>
              </div>
            )}

            {section.id === 'rating' && (
              <div className="space-y-2">
                {ratingOptions.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2 rtl:space-x-reverse">
                    <Checkbox
                      id={`rating-${option.value}`}
                      checked={filters.minRating === option.value}
                      onCheckedChange={() => handleRatingChange(option.value)}
                    />
                    <Label 
                      htmlFor={`rating-${option.value}`}
                      className="text-sm cursor-pointer flex items-center"
                    >
                      <div className="flex items-center mr-2">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star
                            key={i}
                            className={`w-3 h-3 ${
                              i < option.value 
                                ? 'fill-yellow-400 text-yellow-400' 
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      {option.label}
                    </Label>
                  </div>
                ))}
              </div>
            )}

            {section.id === 'location' && (
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">المسافة القصوى: {distanceRange} كم</Label>
                  <Slider
                    value={[distanceRange]}
                    onValueChange={handleDistanceChange}
                    max={100}
                    min={1}
                    step={1}
                    className="w-full mt-2"
                  />
                </div>
              </div>
            )}

            {section.id === 'availability' && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="open-now" className="text-sm">مفتوح الآن</Label>
                  <Switch
                    id="open-now"
                    checked={filters.openNow || false}
                    onCheckedChange={(checked) => handleSwitchChange('openNow', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="has-delivery" className="text-sm">يوفر التوصيل</Label>
                  <Switch
                    id="has-delivery"
                    checked={filters.hasDelivery || false}
                    onCheckedChange={(checked) => handleSwitchChange('hasDelivery', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="fast-delivery" className="text-sm">توصيل سريع</Label>
                  <Switch
                    id="fast-delivery"
                    checked={filters.fastDelivery || false}
                    onCheckedChange={(checked) => handleSwitchChange('fastDelivery', checked)}
                  />
                </div>
              </div>
            )}

            {section.id === 'store' && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="verified-only" className="text-sm">متاجر موثقة فقط</Label>
                  <Switch
                    id="verified-only"
                    checked={filters.verifiedOnly || false}
                    onCheckedChange={(checked) => handleSwitchChange('verifiedOnly', checked)}
                  />
                </div>
              </div>
            )}

            {section.id === 'advanced' && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="has-offers" className="text-sm">يحتوي على عروض</Label>
                  <Switch
                    id="has-offers"
                    checked={filters.hasOffers || false}
                    onCheckedChange={(checked) => handleSwitchChange('hasOffers', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="new-arrivals" className="text-sm">وصل حديثاً</Label>
                  <Switch
                    id="new-arrivals"
                    checked={filters.newArrivals || false}
                    onCheckedChange={(checked) => handleSwitchChange('newArrivals', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="trending" className="text-sm">الأكثر رواجاً</Label>
                  <Switch
                    id="trending"
                    checked={filters.trending || false}
                    onCheckedChange={(checked) => handleSwitchChange('trending', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="featured" className="text-sm">مميز</Label>
                  <Switch
                    id="featured"
                    checked={filters.featured || false}
                    onCheckedChange={(checked) => handleSwitchChange('featured', checked)}
                  />
                </div>
              </div>
            )}
          </CollapsibleContent>
          
          <Separator className="mt-4" />
        </Collapsible>
      ))}
    </div>
  );

  if (isMobile) {
    return (
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="outline" className={className}>
            <Filter className="w-4 h-4 mr-2" />
            فلاتر متقدمة
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                {getActiveFiltersCount()}
              </Badge>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent side="right" className="w-80">
          <SheetHeader>
            <SheetTitle className="flex items-center">
              <Filter className="w-5 h-5 mr-2" />
              فلاتر متقدمة
            </SheetTitle>
          </SheetHeader>
          <ScrollArea className="h-full mt-6">
            <FilterContent />
          </ScrollArea>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Filter className="w-5 h-5 mr-2" />
            فلاتر متقدمة
          </div>
          {getActiveFiltersCount() > 0 && (
            <Badge variant="secondary">
              {getActiveFiltersCount()}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[600px] pr-4">
          <FilterContent />
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
