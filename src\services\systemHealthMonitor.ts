// src/services/systemHealthMonitor.ts - مراقب الصحة العامة للنظام

import { advancedModelManager } from './advancedModelManager';
import { modelOptimizer } from './modelOptimizer';

interface SystemHealth {
  overall: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  score: number; // 0-100
  components: {
    memory: ComponentHealth;
    models: ComponentHealth;
    performance: ComponentHealth;
    optimization: ComponentHealth;
    stability: ComponentHealth;
  };
  alerts: SystemAlert[];
  recommendations: string[];
  lastUpdated: number;
}

interface ComponentHealth {
  status: 'healthy' | 'warning' | 'critical';
  score: number; // 0-100
  metrics: Record<string, any>;
  issues: string[];
}

interface SystemAlert {
  id: string;
  type: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  message: string;
  timestamp: number;
  component: string;
  acknowledged: boolean;
}

interface HealthThresholds {
  memory: {
    warning: number; // نسبة مئوية
    critical: number;
  };
  performance: {
    warning: number; // بالميلي ثانية
    critical: number;
  };
  stability: {
    errorRate: number; // نسبة مئوية
    crashRate: number;
  };
}

/**
 * مراقب الصحة العامة للنظام
 * يراقب جميع مكونات النظام ويقدم تقييماً شاملاً للصحة
 */
export class SystemHealthMonitor {
  private static instance: SystemHealthMonitor;
  private healthHistory: SystemHealth[] = [];
  private alerts: SystemAlert[] = [];
  private monitoringTimer?: NodeJS.Timeout;
  private thresholds: HealthThresholds;

  private constructor() {
    this.thresholds = {
      memory: {
        warning: 75, // 75%
        critical: 90  // 90%
      },
      performance: {
        warning: 3000, // 3 ثواني
        critical: 5000 // 5 ثواني
      },
      stability: {
        errorRate: 10, // 10%
        crashRate: 5   // 5%
      }
    };

    this.startMonitoring();
  }

  static getInstance(): SystemHealthMonitor {
    if (!SystemHealthMonitor.instance) {
      SystemHealthMonitor.instance = new SystemHealthMonitor();
    }
    return SystemHealthMonitor.instance;
  }

  /**
   * بدء مراقبة النظام
   */
  private startMonitoring(): void {
    // مراقبة كل 30 ثانية
    this.monitoringTimer = setInterval(() => {
      this.performHealthCheck();
    }, 30000);

    // فحص أولي
    this.performHealthCheck();
    
    console.log('🏥 تم بدء مراقبة صحة النظام');
  }

  /**
   * إجراء فحص شامل لصحة النظام
   */
  async performHealthCheck(): Promise<SystemHealth> {
    try {
      const timestamp = Date.now();
      
      // فحص مكونات النظام
      const memoryHealth = await this.checkMemoryHealth();
      const modelsHealth = await this.checkModelsHealth();
      const performanceHealth = await this.checkPerformanceHealth();
      const optimizationHealth = await this.checkOptimizationHealth();
      const stabilityHealth = await this.checkStabilityHealth();

      // حساب النقاط الإجمالية
      const overallScore = this.calculateOverallScore({
        memory: memoryHealth,
        models: modelsHealth,
        performance: performanceHealth,
        optimization: optimizationHealth,
        stability: stabilityHealth
      });

      // تحديد الحالة العامة
      const overallStatus = this.determineOverallStatus(overallScore);

      // توليد التوصيات
      const recommendations = this.generateRecommendations({
        memory: memoryHealth,
        models: modelsHealth,
        performance: performanceHealth,
        optimization: optimizationHealth,
        stability: stabilityHealth
      });

      const systemHealth: SystemHealth = {
        overall: overallStatus,
        score: overallScore,
        components: {
          memory: memoryHealth,
          models: modelsHealth,
          performance: performanceHealth,
          optimization: optimizationHealth,
          stability: stabilityHealth
        },
        alerts: this.getActiveAlerts(),
        recommendations,
        lastUpdated: timestamp
      };

      // حفظ في التاريخ
      this.healthHistory.push(systemHealth);
      if (this.healthHistory.length > 100) {
        this.healthHistory.shift();
      }

      // إنشاء تنبيهات جديدة
      this.generateAlerts(systemHealth);

      return systemHealth;

    } catch (error) {
      console.error('❌ خطأ في فحص صحة النظام:', error);
      
      // إرجاع حالة طوارئ
      return this.getEmergencyHealth();
    }
  }

  /**
   * فحص صحة الذاكرة
   */
  private async checkMemoryHealth(): Promise<ComponentHealth> {
    try {
      const memoryStats = advancedModelManager.getDetailedMemoryStats();
      const usagePercentage = (memoryStats.totalUsage / memoryStats.maxUsage) * 100;
      
      let status: ComponentHealth['status'] = 'healthy';
      let score = 100;
      const issues: string[] = [];

      if (usagePercentage >= this.thresholds.memory.critical) {
        status = 'critical';
        score = 20;
        issues.push('استخدام الذاكرة في المنطقة الحرجة');
      } else if (usagePercentage >= this.thresholds.memory.warning) {
        status = 'warning';
        score = 60;
        issues.push('استخدام الذاكرة مرتفع');
      } else {
        score = Math.max(20, 100 - usagePercentage);
      }

      // فحص التجزئة
      if (memoryStats.fragmentedMemory > memoryStats.totalUsage * 0.2) {
        issues.push('تجزئة عالية في الذاكرة');
        score -= 10;
      }

      return {
        status,
        score: Math.max(0, score),
        metrics: {
          usagePercentage,
          totalUsage: memoryStats.totalUsage,
          availableMemory: memoryStats.availableMemory,
          fragmentedMemory: memoryStats.fragmentedMemory,
          loadedModels: memoryStats.loadedModels
        },
        issues
      };
    } catch (error) {
      return {
        status: 'critical',
        score: 0,
        metrics: {},
        issues: ['فشل في فحص الذاكرة']
      };
    }
  }

  /**
   * فحص صحة النماذج
   */
  private async checkModelsHealth(): Promise<ComponentHealth> {
    try {
      const memoryStats = advancedModelManager.getDetailedMemoryStats();
      
      let status: ComponentHealth['status'] = 'healthy';
      let score = 100;
      const issues: string[] = [];

      // فحص عدد النماذج المحملة
      if (memoryStats.loadedModels === 0) {
        status = 'warning';
        score = 40;
        issues.push('لا توجد نماذج محملة');
      } else if (memoryStats.loadedModels < 2) {
        score = 70;
        issues.push('عدد قليل من النماذج المحملة');
      }

      // فحص توزيع النماذج
      const modelTypes = new Set(memoryStats.modelBreakdown.map(m => {
        if (m.id.includes('trocr')) return 'ocr';
        if (m.id.includes('bert')) return 'ner';
        return 'classification';
      }));

      if (modelTypes.size < 3) {
        score -= 15;
        issues.push('لا تغطي النماذج جميع المهام المطلوبة');
      }

      // فحص أداء النماذج
      const lowUsageModels = memoryStats.modelBreakdown.filter(
        m => m.usageCount < 5 && Date.now() - m.lastUsed > 3600000
      );
      
      if (lowUsageModels.length > 0) {
        score -= 10;
        issues.push(`${lowUsageModels.length} نموذج قليل الاستخدام`);
      }

      return {
        status,
        score: Math.max(0, score),
        metrics: {
          loadedModels: memoryStats.loadedModels,
          modelTypes: Array.from(modelTypes),
          lowUsageModels: lowUsageModels.length,
          totalUsage: memoryStats.modelBreakdown.reduce((sum, m) => sum + m.usageCount, 0)
        },
        issues
      };
    } catch (error) {
      return {
        status: 'critical',
        score: 0,
        metrics: {},
        issues: ['فشل في فحص النماذج']
      };
    }
  }

  /**
   * فحص صحة الأداء
   */
  private async checkPerformanceHealth(): Promise<ComponentHealth> {
    // محاكاة فحص الأداء
    const avgResponseTime = Math.random() * 4000 + 1000; // 1-5 ثواني
    const errorRate = Math.random() * 15; // 0-15%
    
    let status: ComponentHealth['status'] = 'healthy';
    let score = 100;
    const issues: string[] = [];

    if (avgResponseTime >= this.thresholds.performance.critical) {
      status = 'critical';
      score = 20;
      issues.push('أوقات الاستجابة بطيئة جداً');
    } else if (avgResponseTime >= this.thresholds.performance.warning) {
      status = 'warning';
      score = 60;
      issues.push('أوقات الاستجابة مرتفعة');
    }

    if (errorRate > this.thresholds.stability.errorRate) {
      status = 'warning';
      score -= 20;
      issues.push('معدل أخطاء مرتفع');
    }

    return {
      status,
      score: Math.max(0, score),
      metrics: {
        avgResponseTime,
        errorRate,
        throughput: Math.random() * 100 + 50 // طلب/دقيقة
      },
      issues
    };
  }

  /**
   * فحص صحة التحسين
   */
  private async checkOptimizationHealth(): Promise<ComponentHealth> {
    try {
      const optimizationReport = modelOptimizer.getOptimizationReport();
      
      let status: ComponentHealth['status'] = 'healthy';
      let score = 100;
      const issues: string[] = [];

      if (optimizationReport.successRate < 70) {
        status = 'warning';
        score = 50;
        issues.push('معدل نجاح التحسين منخفض');
      }

      if (optimizationReport.totalOptimizations < 10) {
        score -= 20;
        issues.push('عدد قليل من عمليات التحسين');
      }

      return {
        status,
        score: Math.max(0, score),
        metrics: {
          totalOptimizations: optimizationReport.totalOptimizations,
          successRate: optimizationReport.successRate,
          totalMemoryFreed: optimizationReport.totalMemoryFreed,
          averagePerformanceGain: optimizationReport.averagePerformanceGain
        },
        issues
      };
    } catch (error) {
      return {
        status: 'warning',
        score: 50,
        metrics: {},
        issues: ['فشل في فحص التحسين']
      };
    }
  }

  /**
   * فحص استقرار النظام
   */
  private async checkStabilityHealth(): Promise<ComponentHealth> {
    // محاكاة فحص الاستقرار
    const uptime = Date.now() - (Date.now() - Math.random() * 86400000); // حتى 24 ساعة
    const crashCount = Math.floor(Math.random() * 3); // 0-2 انهيار
    
    let status: ComponentHealth['status'] = 'healthy';
    let score = 100;
    const issues: string[] = [];

    if (crashCount > 0) {
      status = 'warning';
      score = 70;
      issues.push(`${crashCount} انهيار في آخر 24 ساعة`);
    }

    if (uptime < 3600000) { // أقل من ساعة
      score -= 10;
      issues.push('وقت تشغيل قصير');
    }

    return {
      status,
      score: Math.max(0, score),
      metrics: {
        uptime,
        crashCount,
        lastCrash: crashCount > 0 ? Date.now() - Math.random() * 86400000 : null
      },
      issues
    };
  }

  /**
   * حساب النقاط الإجمالية
   */
  private calculateOverallScore(components: SystemHealth['components']): number {
    const weights = {
      memory: 0.25,
      models: 0.20,
      performance: 0.25,
      optimization: 0.15,
      stability: 0.15
    };

    return Math.round(
      components.memory.score * weights.memory +
      components.models.score * weights.models +
      components.performance.score * weights.performance +
      components.optimization.score * weights.optimization +
      components.stability.score * weights.stability
    );
  }

  /**
   * تحديد الحالة العامة
   */
  private determineOverallStatus(score: number): SystemHealth['overall'] {
    if (score >= 90) return 'excellent';
    if (score >= 75) return 'good';
    if (score >= 60) return 'fair';
    if (score >= 40) return 'poor';
    return 'critical';
  }

  /**
   * توليد التوصيات
   */
  private generateRecommendations(components: SystemHealth['components']): string[] {
    const recommendations: string[] = [];

    // توصيات الذاكرة
    if (components.memory.status === 'critical') {
      recommendations.push('قم بتحرير الذاكرة فوراً - أوقف النماذج غير المستخدمة');
    } else if (components.memory.status === 'warning') {
      recommendations.push('فكر في تحسين استخدام الذاكرة');
    }

    // توصيات النماذج
    if (components.models.score < 70) {
      recommendations.push('احمل النماذج الأساسية لتحسين الأداء');
    }

    // توصيات الأداء
    if (components.performance.status === 'warning') {
      recommendations.push('راجع إعدادات الأداء وحسن التكوين');
    }

    // توصيات التحسين
    if (components.optimization.score < 60) {
      recommendations.push('فعل التحسين التلقائي لتحسين الأداء');
    }

    if (recommendations.length === 0) {
      recommendations.push('النظام يعمل بكفاءة عالية - لا توجد إجراءات مطلوبة');
    }

    return recommendations;
  }

  /**
   * إنشاء تنبيهات جديدة
   */
  private generateAlerts(health: SystemHealth): void {
    // تنبيهات الذاكرة
    if (health.components.memory.status === 'critical') {
      this.addAlert({
        type: 'critical',
        title: 'استخدام ذاكرة حرج',
        message: 'الذاكرة ممتلئة تقريباً - قم بتحرير الذاكرة فوراً',
        component: 'memory'
      });
    }

    // تنبيهات الأداء
    if (health.components.performance.status === 'critical') {
      this.addAlert({
        type: 'error',
        title: 'أداء ضعيف',
        message: 'أوقات الاستجابة بطيئة جداً',
        component: 'performance'
      });
    }

    // تنبيهات الاستقرار
    if (health.components.stability.issues.length > 0) {
      this.addAlert({
        type: 'warning',
        title: 'مشاكل في الاستقرار',
        message: health.components.stability.issues.join(', '),
        component: 'stability'
      });
    }
  }

  /**
   * إضافة تنبيه جديد
   */
  private addAlert(alert: Omit<SystemAlert, 'id' | 'timestamp' | 'acknowledged'>): void {
    const newAlert: SystemAlert = {
      ...alert,
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      acknowledged: false
    };

    this.alerts.push(newAlert);

    // الاحتفاظ بآخر 50 تنبيه فقط
    if (this.alerts.length > 50) {
      this.alerts.shift();
    }
  }

  /**
   * الحصول على التنبيهات النشطة
   */
  private getActiveAlerts(): SystemAlert[] {
    return this.alerts.filter(alert => !alert.acknowledged);
  }

  /**
   * حالة طوارئ
   */
  private getEmergencyHealth(): SystemHealth {
    return {
      overall: 'critical',
      score: 0,
      components: {
        memory: { status: 'critical', score: 0, metrics: {}, issues: ['فشل في الفحص'] },
        models: { status: 'critical', score: 0, metrics: {}, issues: ['فشل في الفحص'] },
        performance: { status: 'critical', score: 0, metrics: {}, issues: ['فشل في الفحص'] },
        optimization: { status: 'critical', score: 0, metrics: {}, issues: ['فشل في الفحص'] },
        stability: { status: 'critical', score: 0, metrics: {}, issues: ['فشل في الفحص'] }
      },
      alerts: [],
      recommendations: ['النظام في حالة طوارئ - أعد تشغيل التطبيق'],
      lastUpdated: Date.now()
    };
  }

  /**
   * الحصول على آخر حالة صحة
   */
  getCurrentHealth(): SystemHealth | null {
    return this.healthHistory[this.healthHistory.length - 1] || null;
  }

  /**
   * الحصول على تاريخ الصحة
   */
  getHealthHistory(limit: number = 24): SystemHealth[] {
    return this.healthHistory.slice(-limit);
  }

  /**
   * الاعتراف بالتنبيه
   */
  acknowledgeAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      return true;
    }
    return false;
  }

  /**
   * إيقاف المراقبة
   */
  stopMonitoring(): void {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = undefined;
      console.log('⏹️ تم إيقاف مراقبة صحة النظام');
    }
  }
}

// تصدير المثيل الوحيد
export const systemHealthMonitor = SystemHealthMonitor.getInstance();
