# AI Document Processing API Documentation

## Overview

The AI Document Processing API provides endpoints for uploading, processing, and managing document analysis using advanced AI models. The system processes documents through three stages: OCR, NER, and Classification.

## Authentication

All API endpoints require authentication using Bearer tokens:

```
Authorization: Bearer <your-auth-token>
```

## Base URL

```
https://your-domain.com/api/ai-document-processing
```

## Endpoints

### 1. Upload Document

Upload a document for AI processing.

**Endpoint:** `POST /upload`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `document` (File, required): The document file to process
- `userType` (string, required): Either "merchant" or "representative"
- `documentType` (string, optional): Type of document for better processing

**Supported File Types:**
- Images: JPEG, PNG, WebP, HEIC
- Documents: PDF (up to 10 pages)

**File Size Limit:** 10MB

**Example Request:**
```javascript
const formData = new FormData();
formData.append('document', fileInput.files[0]);
formData.append('userType', 'merchant');
formData.append('documentType', 'commercial_registration');

const response = await fetch('/api/ai-document-processing/upload', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "تم رفع المستند وبدء المعالجة بنجاح",
  "data": {
    "processingId": "proc_123456789",
    "status": "processing",
    "currentStage": "ocr",
    "estimatedTime": "2-3 دقائق"
  }
}
```

**Error Responses:**

**400 - Bad Request:**
```json
{
  "success": false,
  "error": "نوع الملف غير مدعوم",
  "details": {
    "supportedTypes": ["JPG", "PNG", "WebP", "HEIC", "PDF"],
    "receivedType": "text/plain"
  }
}
```

**401 - Unauthorized:**
```json
{
  "success": false,
  "error": "غير مصرح بالوصول"
}
```

**413 - File Too Large:**
```json
{
  "success": false,
  "error": "حجم الملف كبير جداً",
  "details": {
    "maxSize": "10 ميجابايت",
    "currentSize": "15.2 ميجابايت"
  }
}
```

### 2. Get Processing Status

Check the status of a document processing operation.

**Endpoint:** `GET /status/{processingId}`

**Parameters:**
- `processingId` (string, required): The processing ID returned from upload

**Example Request:**
```javascript
const response = await fetch(`/api/ai-document-processing/status/${processingId}`, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "proc_123456789",
    "status": "completed",
    "currentStage": "completed",
    "overallConfidence": 0.85,
    "stages": {
      "ocr": {
        "completed": true,
        "confidence": 0.88,
        "processingTime": 1200,
        "modelUsed": "microsoft/trocr-base-printed"
      },
      "ner": {
        "completed": true,
        "confidence": 0.82,
        "processingTime": 800,
        "modelUsed": "aubmindlab/bert-base-arabertv02"
      },
      "classification": {
        "completed": true,
        "confidence": 0.85,
        "processingTime": 600,
        "modelUsed": "microsoft/DialoGPT-large"
      }
    },
    "results": {
      "decision": "approve",
      "documentType": "commercial_registration",
      "extractedData": {
        "businessName": "شركة التجارة المتقدمة",
        "ownerName": "أحمد محمد علي",
        "registrationNumber": "1234567890",
        "issueDate": "2023-01-15",
        "expiryDate": "2025-01-15"
      },
      "reasons": [
        "جميع البيانات المطلوبة متوفرة وصحيحة",
        "المستند ضمن معايير الجودة المقبولة"
      ],
      "riskScore": 15
    },
    "qualityCheck": {
      "imageQuality": 0.9,
      "blurDetected": false,
      "resolutionSufficient": true,
      "formatSupported": true
    },
    "errors": [],
    "warnings": [],
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:32:30Z",
    "completedAt": "2024-01-15T10:32:30Z",
    "fileInfo": {
      "originalFileName": "commercial-registration.jpg",
      "fileSize": 2048576,
      "mimeType": "image/jpeg"
    }
  }
}
```

**Processing Status Values:**
- `processing`: Document is being processed
- `completed`: Processing completed successfully
- `failed`: Processing failed
- `requires_reupload`: Document quality insufficient, reupload needed

**Current Stage Values:**
- `ocr`: Text extraction in progress
- `ner`: Data extraction in progress
- `classification`: Document classification in progress
- `completed`: All stages completed

### 3. Get Processing History

Retrieve the processing history for the authenticated user.

**Endpoint:** `GET /history`

**Query Parameters:**
- `limit` (number, optional): Number of records to return (default: 20, max: 100)
- `status` (string, optional): Filter by status
- `userType` (string, optional): Filter by user type
- `lastDocId` (string, optional): For pagination

**Example Request:**
```javascript
const response = await fetch('/api/ai-document-processing/history?limit=10&status=completed', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "documents": [
      {
        "id": "proc_123456789",
        "status": "completed",
        "currentStage": "completed",
        "userType": "merchant",
        "originalFileName": "commercial-reg.jpg",
        "fileSize": 2048576,
        "mimeType": "image/jpeg",
        "overallConfidence": 0.85,
        "results": {
          "decision": "approve",
          "documentType": "commercial_registration",
          "riskScore": 15
        },
        "qualityCheck": {
          "imageQuality": 0.9,
          "blurDetected": false,
          "resolutionSufficient": true,
          "formatSupported": true
        },
        "errorsCount": 0,
        "warningsCount": 0,
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T10:32:30Z",
        "completedAt": "2024-01-15T10:32:30Z",
        "processingStats": {
          "ocrTime": 1200,
          "nerTime": 800,
          "classificationTime": 600,
          "totalTime": 2600
        }
      }
    ],
    "stats": {
      "total": 25,
      "completed": 20,
      "processing": 2,
      "failed": 1,
      "requiresReupload": 2,
      "averageConfidence": 0.82,
      "averageProcessingTime": 2800
    },
    "pagination": {
      "hasMore": true,
      "lastDocId": "proc_123456789"
    }
  }
}
```

## Data Models

### OCRResult
```typescript
interface OCRResult {
  extractedText: string;
  confidence: number;
  language: 'ar' | 'en' | 'mixed';
  processingTime: number;
  modelUsed: string;
}
```

### NERResult
```typescript
interface NERResult {
  entities: Array<{
    text: string;
    label: string;
    confidence: number;
    start: number;
    end: number;
  }>;
  extractedData: Record<string, any>;
  confidence: number;
  processingTime: number;
  modelUsed: string;
}
```

### ClassificationResult
```typescript
interface ClassificationResult {
  documentType: string;
  decision: 'approve' | 'reject' | 'manual_review';
  confidence: number;
  reasons: string[];
  riskScore: number;
  processingTime: number;
  modelUsed: string;
}
```

### QualityCheck
```typescript
interface QualityCheck {
  imageQuality: number;
  blurDetected: boolean;
  resolutionSufficient: boolean;
  formatSupported: boolean;
}
```

## Error Handling

### Common Error Codes

**400 - Bad Request**
- Invalid file format
- File too large
- Missing required parameters
- Invalid user type

**401 - Unauthorized**
- Missing or invalid authentication token
- Token expired

**403 - Forbidden**
- Insufficient permissions
- Access to resource denied

**404 - Not Found**
- Processing ID not found
- Resource does not exist

**429 - Too Many Requests**
- Rate limit exceeded
- Too many concurrent uploads

**500 - Internal Server Error**
- AI service unavailable
- Database connection error
- Unexpected server error

### Error Response Format
```json
{
  "success": false,
  "error": "Error message in user's language",
  "code": "ERROR_CODE",
  "details": {
    "additionalInfo": "value"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Rate Limiting

- **Upload**: 10 requests per minute per user
- **Status Check**: 60 requests per minute per user
- **History**: 30 requests per minute per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 8
X-RateLimit-Reset: 1642248600
```

## Webhooks (Future Feature)

Webhooks will be available to receive real-time notifications about processing status changes.

**Webhook Events:**
- `processing.started`
- `processing.stage_completed`
- `processing.completed`
- `processing.failed`
- `processing.requires_reupload`

## SDKs and Libraries

### JavaScript/TypeScript
```javascript
import { AIDocumentProcessor } from '@mikhla/ai-document-processing';

const processor = new AIDocumentProcessor({
  apiKey: 'your-api-key',
  baseUrl: 'https://your-domain.com/api'
});

// Upload document
const result = await processor.upload(file, {
  userType: 'merchant',
  documentType: 'commercial_registration'
});

// Check status
const status = await processor.getStatus(result.processingId);

// Get history
const history = await processor.getHistory({ limit: 10 });
```

### Python (Planned)
```python
from mikhla_ai import DocumentProcessor

processor = DocumentProcessor(api_key='your-api-key')

# Upload document
result = processor.upload(
    file_path='document.jpg',
    user_type='merchant',
    document_type='commercial_registration'
)

# Check status
status = processor.get_status(result.processing_id)
```

## Testing

### Test Environment
Base URL: `https://test.your-domain.com/api/ai-document-processing`

### Sample Test Files
Test files are available for different scenarios:
- `test-commercial-reg-clear.jpg` - High quality commercial registration
- `test-commercial-reg-blurry.jpg` - Low quality image
- `test-national-id.jpg` - National ID sample
- `test-driving-license.pdf` - PDF driving license

### Mock Responses
Test environment provides predictable responses for testing:
- Files with "approve" in name → automatic approval
- Files with "reject" in name → automatic rejection
- Files with "review" in name → manual review required
- Files with "error" in name → processing error
