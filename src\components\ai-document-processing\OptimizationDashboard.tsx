'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Zap, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Settings,
  Play,
  Pause,
  BarChart3,
  Cpu,
  HardDrive,
  Activity,
  RefreshCw
} from 'lucide-react';

interface OptimizationMetrics {
  totalOptimizations: number;
  successRate: number;
  totalMemoryFreed: number;
  averagePerformanceGain: number;
  recentOptimizations: Array<{
    timestamp: number;
    strategy: string;
    result: {
      success: boolean;
      message: string;
      memoryFreed?: number;
      performanceGain?: number;
    };
  }>;
  recommendations: string[];
}

interface OptimizationConfig {
  enableAutoOptimization: boolean;
  optimizationInterval: number;
  memoryThreshold: number;
  performanceThreshold: number;
  aggressiveMode: boolean;
}

interface OptimizationStrategy {
  name: string;
  description: string;
  priority: number;
}

interface OptimizationDashboardProps {
  onRunOptimization?: (strategies?: string[]) => Promise<void>;
  onUpdateConfig?: (config: Partial<OptimizationConfig>) => Promise<void>;
  className?: string;
}

export default function OptimizationDashboard({
  onRunOptimization,
  onUpdateConfig,
  className = ''
}: OptimizationDashboardProps) {
  const [metrics, setMetrics] = useState<OptimizationMetrics | null>(null);
  const [config, setConfig] = useState<OptimizationConfig>({
    enableAutoOptimization: true,
    optimizationInterval: 2 * 60 * 1000,
    memoryThreshold: 80,
    performanceThreshold: 5000,
    aggressiveMode: false
  });
  const [strategies] = useState<OptimizationStrategy[]>([
    {
      name: 'memory_cleanup',
      description: 'تنظيف الذاكرة من النماذج غير المستخدمة',
      priority: 1
    },
    {
      name: 'cache_optimization',
      description: 'تحسين الكاش وإزالة البيانات القديمة',
      priority: 2
    },
    {
      name: 'model_preloading',
      description: 'تحميل النماذج المتوقع استخدامها مسبقاً',
      priority: 3
    },
    {
      name: 'performance_tuning',
      description: 'ضبط إعدادات الأداء بناءً على الاستخدام',
      priority: 4
    },
    {
      name: 'garbage_collection',
      description: 'تشغيل جمع القمامة لتحرير الذاكرة',
      priority: 5
    }
  ]);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [selectedStrategies, setSelectedStrategies] = useState<string[]>([]);

  // محاكاة تحميل البيانات
  useEffect(() => {
    loadMetrics();
    const interval = setInterval(loadMetrics, 5000);
    return () => clearInterval(interval);
  }, []);

  const loadMetrics = () => {
    // محاكاة البيانات (في التطبيق الحقيقي ستأتي من modelOptimizer)
    const mockMetrics: OptimizationMetrics = {
      totalOptimizations: Math.floor(Math.random() * 100) + 50,
      successRate: 85 + Math.random() * 10,
      totalMemoryFreed: Math.random() * 500 * 1024 * 1024, // حتى 500MB
      averagePerformanceGain: Math.random() * 2000 + 500,
      recentOptimizations: generateMockOptimizations(),
      recommendations: generateMockRecommendations()
    };
    setMetrics(mockMetrics);
  };

  const generateMockOptimizations = () => {
    const optimizations = [];
    for (let i = 0; i < 5; i++) {
      optimizations.push({
        timestamp: Date.now() - Math.random() * 3600000,
        strategy: strategies[Math.floor(Math.random() * strategies.length)].name,
        result: {
          success: Math.random() > 0.2,
          message: 'تم التحسين بنجاح',
          memoryFreed: Math.random() * 50 * 1024 * 1024,
          performanceGain: Math.random() * 1000
        }
      });
    }
    return optimizations;
  };

  const generateMockRecommendations = () => {
    const allRecommendations = [
      'النظام يعمل بكفاءة عالية',
      'فكر في تفعيل الوضع العدواني لتحسين أفضل',
      'معدل نجاح التحسين ممتاز',
      'الذاكرة تستخدم بكفاءة',
      'أوقات الاستجابة ضمن المعدل المطلوب'
    ];
    return allRecommendations.slice(0, Math.floor(Math.random() * 3) + 1);
  };

  const handleRunOptimization = async (strategyNames?: string[]) => {
    setIsOptimizing(true);
    try {
      if (onRunOptimization) {
        await onRunOptimization(strategyNames);
      }
      // محاكاة وقت التحسين
      await new Promise(resolve => setTimeout(resolve, 2000));
      loadMetrics(); // تحديث البيانات
    } catch (error) {
      console.error('خطأ في التحسين:', error);
    } finally {
      setIsOptimizing(false);
    }
  };

  const handleConfigUpdate = async (updates: Partial<OptimizationConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    
    if (onUpdateConfig) {
      await onUpdateConfig(updates);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours} ساعة`;
    if (minutes > 0) return `${minutes} دقيقة`;
    return 'الآن';
  };

  const getStrategyIcon = (strategyName: string) => {
    switch (strategyName) {
      case 'memory_cleanup':
        return <HardDrive className="h-4 w-4" />;
      case 'cache_optimization':
        return <RefreshCw className="h-4 w-4" />;
      case 'model_preloading':
        return <Zap className="h-4 w-4" />;
      case 'performance_tuning':
        return <Cpu className="h-4 w-4" />;
      case 'garbage_collection':
        return <Activity className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  if (!metrics) {
    return (
      <div className={`flex items-center justify-center py-8 ${className}`}>
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        <span>جاري تحميل بيانات التحسين...</span>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* بطاقة الإحصائيات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">إجمالي التحسينات</p>
                <p className="text-2xl font-bold">{metrics.totalOptimizations}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">معدل النجاح</p>
                <p className="text-2xl font-bold">{metrics.successRate.toFixed(1)}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">ذاكرة محررة</p>
                <p className="text-2xl font-bold">{formatBytes(metrics.totalMemoryFreed)}</p>
              </div>
              <HardDrive className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">تحسن الأداء</p>
                <p className="text-2xl font-bold">{metrics.averagePerformanceGain.toFixed(0)}ms</p>
              </div>
              <Zap className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* إعدادات التحسين */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            إعدادات التحسين
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">التحسين التلقائي</div>
                  <div className="text-sm text-gray-500">تشغيل التحسين بشكل دوري</div>
                </div>
                <Switch
                  checked={config.enableAutoOptimization}
                  onCheckedChange={(checked) => 
                    handleConfigUpdate({ enableAutoOptimization: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">الوضع العدواني</div>
                  <div className="text-sm text-gray-500">تحسين أكثر قوة وتكراراً</div>
                </div>
                <Switch
                  checked={config.aggressiveMode}
                  onCheckedChange={(checked) => 
                    handleConfigUpdate({ aggressiveMode: checked })
                  }
                />
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>حد الذاكرة للتحسين</span>
                  <span>{config.memoryThreshold}%</span>
                </div>
                <Progress value={config.memoryThreshold} className="h-2" />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>فترة التحسين</span>
                  <span>{config.optimizationInterval / 60000} دقيقة</span>
                </div>
                <Progress value={(config.optimizationInterval / 300000) * 100} className="h-2" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* استراتيجيات التحسين */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Cpu className="h-5 w-5" />
              استراتيجيات التحسين
            </CardTitle>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleRunOptimization(selectedStrategies.length > 0 ? selectedStrategies : undefined)}
                disabled={isOptimizing}
              >
                {isOptimizing ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    جاري التحسين...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    تشغيل التحسين
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {strategies.map((strategy) => (
              <div key={strategy.name} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={selectedStrategies.includes(strategy.name)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedStrategies([...selectedStrategies, strategy.name]);
                      } else {
                        setSelectedStrategies(selectedStrategies.filter(s => s !== strategy.name));
                      }
                    }}
                    className="rounded"
                  />
                  {getStrategyIcon(strategy.name)}
                  <div>
                    <div className="font-medium">{strategy.description}</div>
                    <div className="text-sm text-gray-500">أولوية {strategy.priority}</div>
                  </div>
                </div>
                <Badge variant="outline">
                  {strategy.name.replace('_', ' ')}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* التحسينات الأخيرة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            التحسينات الأخيرة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {metrics.recentOptimizations.map((optimization, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {optimization.result.success ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-500" />
                  )}
                  <div>
                    <div className="font-medium">
                      {strategies.find(s => s.name === optimization.strategy)?.description || optimization.strategy}
                    </div>
                    <div className="text-sm text-gray-500">
                      {optimization.result.message}
                    </div>
                  </div>
                </div>
                <div className="text-right text-sm">
                  <div>{formatTimeAgo(optimization.timestamp)}</div>
                  {optimization.result.memoryFreed && (
                    <div className="text-gray-500">
                      {formatBytes(optimization.result.memoryFreed)}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* التوصيات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            التوصيات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {metrics.recommendations.map((recommendation, index) => (
              <Alert key={index}>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{recommendation}</AlertDescription>
              </Alert>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
